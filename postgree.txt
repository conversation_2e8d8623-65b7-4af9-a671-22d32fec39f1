sudo -u postgres psql

# Connect to PostgreSQL | Cách xử lý Database Timezone:
psql -U postgres -d testlivesport

# Set database timezone
ALTER DATABASE testlivesport SET timezone TO 'UTC';

# Verify
SHOW timezone;


redis: redis-cli
rgUWBodt9jUwk01jakjhsdfDULrcWJ4

De su dung đc data:
CMS cần:
- L<PERSON><PERSON> đ<PERSON> list theo season là năm muốn, để lấy tên giai đấu http://localhost:3000/football/leagues?season=2025&newdb=true => active giải đấu mình cần lấy.
    + Hỗ trợ tạo đc giải đấu.
- Lấy list trận đấu mới nhất theo giải: http://localhost:3000/football/fixtures?league=850&season=2025&newdb=true

    AutoUpdateSportsGame auto lấy list mùa giải đang active: 
        http://localhost:3000/football/leagues?season=2025&newdb=true
        http://localhost:3000/football/leagues?season=2024&newdb=true
        => năm hiện tại và năm trư<PERSON>c đó, để có các trận đấu theo utc, sau đó lấy danh sách trận đấu theo năm cảu giải đấu http://localhost:3000/football/fixtures?league=255&season=2024&newdb=true
- Người dùng edit và save.

GET http://localhost:3000/football/teams/statistics?league=850&season=2025&team=8177

http://localhost:3000/football/teams?league=850&season=2025 = lấy dc teamid  (externalId)
=> http://localhost:3000/football/teams/statistics?league=850&season=2025&team={externalId}

Link play vs link comment theo trận đấu: 
- Detail http://localhost:3000/football/fixtures/1018033
- link play vs binh luan: http://localhost:3000/broadcast-links/fixture/1018033


Chức năng: Lấy thống kê số trận theo trạng thái (NS, FT, v.v.) hoặc đội.
/football/statistics/matches:
            


Lấy lịch thi đấu của một đội theo teamId. Chức năng: Lấy lịch thi đấu của đội theo teamId, hỗ trợ lọc theo season, dateFrom, dateTo, phân trang.
http://localhost:3000/football/fixtures/schedules/8177 


Kiem tra dong bo:
http://localhost:3000/football/fixtures/sync/status
http://localhost:3000/football/fixtures?league=850&season=2025
http://localhost:3000/football/fixtures?date=2025-05-22&newdb=true
http://localhost:3000/football/fixtures?status=NS
http://localhost:3000/football/fixtures/1301502

http://localhost:3000/football/fixtures/upcoming-and-live

Cập nhật thủ công trang thái của các trận đấu ( của league đang active): thủ công GET http://localhost:3000/football/fixtures/sync/fixtures
Cập nhật thủ công các giải đấu của league đang active: GET http://localhost:3000/football/fixtures/sync/daily


Lich thi đấu theo ngày của 1 mùa giải: http://localhost:3000/football/fixtures?league=570&limit=100&date=2025-05-23
Lich thi đấu theo ngày của 1 mùa giải thoe status: http://localhost:3000/football/fixtures?league=570&limit=100&date=2025-05-23&status=NS
Deploy:
npm run build
pm2 start ecosystem.config.js



CREATE USER postgresuser WITH PASSWORD '831993da';
DROP DATABASE IF EXISTS testlivesport;
CREATE DATABASE testlivesport OWNER postgresuser;
