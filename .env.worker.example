API_FOOTBALL_URL=https://v3.football.api-sports.io
API_FOOTBALL_KEY=8f89922a05236e68a46e5c0744ac94f7
API_RETRY_ATTEMPTS=3
API_RETRY_DELAY=1000
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=rgUWBodt9jUwk01jakjhsdfDULrcWJ4
DB_HOST=localhost
DB_USER=postgresuser
DB_PASSWORD=831993da
DB_NAME=testlivesport
DB_PORT=5432
IMAGE_STORAGE_PATH=./public/images
FE_DOMAIN=https://vndsport.live

# Image Download Configuration
IMAGE_DOWNLOAD_INTERVAL=1000
IMAGE_DOWNLOAD_MAX_RETRIES=5
IMAGE_DOWNLOAD_RETRY_DELAY=2000
IMAGE_DOWNLOAD_TIMEOUT=30000

# Image Queue Configuration
IMAGE_QUEUE_CLEANUP_HOURS=24
IMAGE_QUEUE_MAX_RETRIES=3
IMAGE_QUEUE_TEAMS_PRIORITY=high
IMAGE_QUEUE_LEAGUES_PRIORITY=high
IMAGE_QUEUE_VENUES_PRIORITY=normal
IMAGE_QUEUE_FLAGS_PRIORITY=low

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-2024
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production-2024
JWT_ACCESS_EXPIRES_IN=1d
JWT_REFRESH_EXPIRES_IN=7d

# Email Configuration
EMAIL_ENABLED=false                          # Bật/tắt email service
SMTP_HOST=smtp.gmail.com                    # SMTP server (Gmail example)
SMTP_PORT=587                               # SMTP port
SMTP_SECURE=false                           # TLS/SSL (false cho port 587)
SMTP_USER=<EMAIL>          # Email tài khoản gửi
SMTP_PASS=your-app-password                 # Mật khẩu ứng dụng (App Password)
SMTP_FROM=<EMAIL>            # Email hiển thị khi gửi

# Application Configuration  
APP_NAME=APISportsGame                      # Tên ứng dụng trong email
FRONTEND_URL=http://localhost:3001          # URL frontend để tạo links
SUPPORT_EMAIL=<EMAIL>     # Email hỗ trợ