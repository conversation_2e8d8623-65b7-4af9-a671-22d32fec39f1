import { MigrationInterface, QueryRunner, Table, Index } from 'typeorm';

export class CreateFixtureViewsTable1719504000000 implements MigrationInterface {
    name = 'CreateFixtureViewsTable1719504000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create fixture_views table
        await queryRunner.createTable(
            new Table({
                name: 'fixture_views',
                columns: [
                    {
                        name: 'id',
                        type: 'serial',
                        isPrimary: true,
                    },
                    {
                        name: 'fixtureId',
                        type: 'integer',
                        isNullable: false,
                    },
                    {
                        name: 'userId',
                        type: 'integer',
                        isNullable: true,
                    },
                    {
                        name: 'ipAddress',
                        type: 'inet',
                        isNullable: false,
                    },
                    {
                        name: 'userAgent',
                        type: 'text',
                        isNullable: true,
                    },
                    {
                        name: 'sessionId',
                        type: 'varchar',
                        length: '100',
                        isNullable: true,
                    },
                    {
                        name: 'viewDate',
                        type: 'date',
                        default: 'CURRENT_DATE',
                    },
                    {
                        name: 'createdAt',
                        type: 'timestamp with time zone',
                        default: 'CURRENT_TIMESTAMP',
                    },
                ],
                foreignKeys: [
                    {
                        columnNames: ['fixtureId'],
                        referencedTableName: 'fixtures',
                        referencedColumnNames: ['id'],
                        onDelete: 'CASCADE',
                    },
                ],
            }),
        );

        // Create indexes
        await queryRunner.query(`
            CREATE INDEX idx_fixture_views_fixture_id ON fixture_views(fixture_id);
            CREATE INDEX idx_fixture_views_user_id ON fixture_views(user_id);
            CREATE INDEX idx_fixture_views_ip_address ON fixture_views(ip_address);
            CREATE INDEX idx_fixture_views_view_date ON fixture_views(view_date);
            CREATE INDEX idx_fixture_views_created_at ON fixture_views(created_at);
        `);

        // Unique constraint for authenticated users (one view per user per day)
        await queryRunner.query(`
            CREATE UNIQUE INDEX unique_user_fixture_daily_view 
            ON fixture_views(fixture_id, user_id, view_date) 
            WHERE user_id IS NOT NULL;
        `);

        // Unique constraint for anonymous users (one view per IP per day)
        await queryRunner.query(`
            CREATE UNIQUE INDEX unique_ip_fixture_daily_view 
            ON fixture_views(fixture_id, ip_address, view_date);
        `);

        // Add view tracking columns to fixtures table
        await queryRunner.query(`
            ALTER TABLE fixtures 
            ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0,
            ADD COLUMN IF NOT EXISTS hot_since TIMESTAMP WITH TIME ZONE;
        `);

        // Create index for view_count
        await queryRunner.query(`
            CREATE INDEX idx_fixtures_view_count ON fixtures(view_count);
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes on fixtures
        await queryRunner.dropIndex('fixtures', 'idx_fixtures_view_count');

        // Remove columns from fixtures
        await queryRunner.query(`
            ALTER TABLE fixtures 
            DROP COLUMN IF EXISTS view_count,
            DROP COLUMN IF EXISTS hot_since;
        `);

        // Drop fixture_views table (indexes will be dropped automatically)
        await queryRunner.dropTable('fixture_views');
    }
}
