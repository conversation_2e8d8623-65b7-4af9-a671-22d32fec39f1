import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class CreateUserFavoritesTable1719505000000 implements MigrationInterface {
    name = 'CreateUserFavoritesTable1719505000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create user_favorites table
        await queryRunner.createTable(
            new Table({
                name: 'user_favorites',
                columns: [
                    {
                        name: 'id',
                        type: 'serial',
                        isPrimary: true,
                    },
                    {
                        name: 'userId',
                        type: 'integer',
                        isNullable: false,
                    },
                    {
                        name: 'entityType',
                        type: 'enum',
                        enum: ['team', 'league', 'player'],
                        isNullable: false,
                    },
                    {
                        name: 'entityId',
                        type: 'integer',
                        isNullable: false,
                    },
                    {
                        name: 'entityName',
                        type: 'varchar',
                        length: '255',
                        isNullable: true,
                    },
                    {
                        name: 'entityLogo',
                        type: 'varchar',
                        length: '500',
                        isNullable: true,
                    },
                    {
                        name: 'isActive',
                        type: 'boolean',
                        default: true,
                    },
                    {
                        name: 'notifyFixtures',
                        type: 'boolean',
                        default: true,
                    },
                    {
                        name: 'notifyNews',
                        type: 'boolean',
                        default: false,
                    },
                    {
                        name: 'notifyTransfers',
                        type: 'boolean',
                        default: false,
                    },
                    {
                        name: 'createdAt',
                        type: 'timestamp with time zone',
                        default: 'now()',
                    },
                    {
                        name: 'updatedAt',
                        type: 'timestamp with time zone',
                        default: 'now()',
                    },
                ],
                foreignKeys: [
                    {
                        columnNames: ['userId'],
                        referencedTableName: 'registered_users',
                        referencedColumnNames: ['id'],
                        onDelete: 'CASCADE',
                    },
                ],
                uniques: [
                    {
                        name: 'UQ_user_favorite_unique',
                        columnNames: ['userId', 'entityType', 'entityId'],
                    },
                ],
            }),
            true
        );

        // Create indexes for performance
        await queryRunner.createIndex('user_favorites', new TableIndex({
            name: 'idx_user_favorite_user_id',
            columnNames: ['userId']
        }));
        await queryRunner.createIndex('user_favorites', new TableIndex({
            name: 'idx_user_favorite_entity_type',
            columnNames: ['entityType']
        }));
        await queryRunner.createIndex('user_favorites', new TableIndex({
            name: 'idx_user_favorite_entity_id',
            columnNames: ['entityId']
        }));
        await queryRunner.createIndex('user_favorites', new TableIndex({
            name: 'idx_user_favorite_created_at',
            columnNames: ['createdAt']
        }));
        await queryRunner.createIndex('user_favorites', new TableIndex({
            name: 'idx_user_favorite_user_entity',
            columnNames: ['userId', 'entityType']
        }));
        await queryRunner.createIndex('user_favorites', new TableIndex({
            name: 'idx_user_favorite_entity_type_id',
            columnNames: ['entityType', 'entityId']
        }));
        await queryRunner.createIndex('user_favorites', new TableIndex({
            name: 'idx_user_favorite_active_notify',
            columnNames: ['isActive', 'notifyFixtures']
        }));
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes first
        await queryRunner.dropIndex('user_favorites', 'idx_user_favorite_active_notify');
        await queryRunner.dropIndex('user_favorites', 'idx_user_favorite_entity_type_id');
        await queryRunner.dropIndex('user_favorites', 'idx_user_favorite_user_entity');
        await queryRunner.dropIndex('user_favorites', 'idx_user_favorite_created_at');
        await queryRunner.dropIndex('user_favorites', 'idx_user_favorite_entity_id');
        await queryRunner.dropIndex('user_favorites', 'idx_user_favorite_entity_type');
        await queryRunner.dropIndex('user_favorites', 'idx_user_favorite_user_id');
        
        // Drop table
        await queryRunner.dropTable('user_favorites');
    }
}
