import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FixtureView } from '../entities/fixture-view.entity';
import { Fixture } from '../models/fixture.entity';

@ApiTags('Test - Database Schema')
@Controller('test/database')
export class TestController {
  constructor(
    @InjectRepository(FixtureView)
    private readonly viewRepository: Repository<FixtureView>,
    @InjectRepository(Fixture)
    private readonly fixtureRepository: Repository<Fixture>,
  ) {}

  @ApiOperation({
    summary: 'Test Database Schema - Check if tables exist',
  })
  @Get('schema-check')
  async checkSchema() {
    try {
      // Check fixture_views table
      const viewCount = await this.viewRepository.count();
      
      // Check fixtures table with new columns
      const fixtureWithViewCount = await this.fixtureRepository
        .createQueryBuilder('fixture')
        .select(['fixture.id', 'fixture.viewCount', 'fixture.hotSince'])
        .limit(1)
        .getOne();
      
      return {
        status: 'success',
        data: {
          fixture_views_table: 'exists',
          fixture_views_count: viewCount,
          fixtures_table: 'exists',
          fixtures_new_columns: {
            viewCount: fixtureWithViewCount?.viewCount || 0,
            hotSince: fixtureWithViewCount?.hotSince || null,
          },
        },
      };
    } catch (error) {
      return {
        status: 'error',
        message: error.message,
      };
    }
  }
}
