import { Controller, Get, Query, Param, UseGuards, Post, Put, Delete, Body, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiQuery, ApiParam, ApiBody } from '@nestjs/swagger';
import { TeamService } from '../services/team.service';
import { TeamStatisticsService } from '../services/team-statistics.service';
import { GetTeamStatisticsDto, TeamStatisticsResponseDto } from '../models/team-statistics.dto';
import { GetTeamsDto, PaginatedTeamsResponse, TeamResponseDto, GetTeamLeaguesAndSeasonsDto, TeamLeaguesAndSeasonsDto, CreateTeamDto, UpdateTeamDto } from '../models/team.dto';

import { SystemJwtAuthGuard } from '../../../auth/system/guards/system-jwt-auth.guard';
import { SystemRolesGuard } from '../../../auth/system/guards/system-roles.guard';
import { TierAccessGuard } from '../../../auth/users/guards/tier-access.guard';
import { Public, GetCurrentUser, EditorPlus, AdminOnly } from '../../../auth/core/decorators/auth.decorators';
import { SystemUser } from '../../../auth/system/entities/system-user.entity';

@ApiTags('Football - Teams')
@Controller('football/teams')
@UseGuards(SystemJwtAuthGuard, SystemRolesGuard, TierAccessGuard)
export class TeamController {
    constructor(
        private readonly teamService: TeamService,
        private readonly teamStatisticsService: TeamStatisticsService
    ) { }

    @ApiOperation({
        summary: 'Get Team Statistics',
        description: `
        Retrieve detailed team statistics for a specific league and season.

        **Features:**
        - Complete team performance data
        - League-specific statistics
        - Season-based analysis
        - Authentication required for API usage tracking

        **Tier Access:**
        - Free: 100 API calls/month
        - Premium: 10,000 API calls/month
        - Enterprise: Unlimited API calls

        **Required Parameters:**
        - league: League ID (e.g., 39 for Premier League)
        - season: Season year (e.g., 2024)
        - team: Team ID (e.g., 33 for Manchester United)

        **Examples:**
        - ?league=39&season=2024&team=33 (Manchester United in Premier League 2024)
        - ?league=140&season=2024&team=529 (Barcelona in La Liga 2024)
        `
    })
    @ApiQuery({ name: 'league', required: true, type: Number, description: 'League ID', example: 39 })
    @ApiQuery({ name: 'season', required: true, type: Number, description: 'Season year', example: 2024 })
    @ApiQuery({ name: 'team', required: true, type: Number, description: 'Team ID', example: 33 })
    @ApiResponse({
        status: 200,
        description: 'Team statistics retrieved successfully',
        example: {
            data: {
                teamId: 33,
                leagueId: 39,
                season: 2024,
                fixtures: {
                    played: { home: 19, away: 19, total: 38 },
                    wins: { home: 12, away: 8, total: 20 },
                    draws: { home: 4, away: 6, total: 10 },
                    loses: { home: 3, away: 5, total: 8 }
                },
                goals: {
                    for: { total: { home: 35, away: 22, total: 57 } },
                    against: { total: { home: 18, away: 25, total: 43 } }
                }
            },
            status: 200
        }
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid query parameters',
        example: {
            message: 'league, season, and team parameters are required',
            error: 'Bad Request',
            statusCode: 400
        }
    })
    @ApiResponse({
        status: 401,
        description: 'Unauthorized - Authentication required',
        example: {
            message: 'System authentication required',
            error: 'Unauthorized',
            statusCode: 401
        }
    })
    @ApiResponse({
        status: 404,
        description: 'Team statistics not found',
        example: {
            message: 'Team statistics not found for the specified parameters',
            error: 'Not Found',
            statusCode: 404
        }
    })
    @Public()
    @Get('statistics')
    async getTeamStatistics(
        @Query() query: GetTeamStatisticsDto,
    ): Promise<{ data: TeamStatisticsResponseDto; status: number }> {
        return this.teamStatisticsService.getTeamStatistics(query);
    }

    @ApiOperation({
        summary: 'Get Teams with Filters (Public)',
        description: `
        Retrieve teams with comprehensive filtering options.

        **🔓 PUBLIC ACCESS:**
        This endpoint is publicly accessible and does not require authentication.

        **Features:**
        - Complete team database
        - League-based filtering
        - Country-based filtering
        - Search by team name, country, or code
        - Pagination support
        - No authentication required

        **Query Parameters:**
        - page, limit: Pagination
        - league, season: Filter by league/season
        - country: Filter by country name
        - search: Search by team name, country, or code
        - newdb: Force fetch from API and update database (optional, default: false)

        **Search Examples:**
        - ?search=Manchester (Find Manchester United, Manchester City)
        - ?search=United (Find Manchester United, Newcastle United, etc.)
        - ?search=MUN (Find by team code)
        - ?search=England (Find English teams)
        - ?search=Flamengo&newdb=true (Force fetch Flamengo from API)

        **Filter Examples:**
        - ?league=39&season=2024 (Premier League 2024 teams)
        - ?country=England (All English teams)
        - ?league=140&season=2024 (La Liga 2024 teams)
        - ?search=Flamengo&newdb=true (Force refresh Flamengo data)

        **Use Cases:**
        - Team directory for mobile apps
        - Website team listings
        - Third-party integrations
        - Sports data aggregation
        - Public team information access

        **Note:** Returns both API teams and manually added teams.
        Manual teams will have externalId = null and no statistics data.

        **Example Request:**
        \`\`\`
        GET /football/teams?league=39&season=2024 HTTP/1.1
        \`\`\`
        `
    })
    @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number', example: 1 })
    @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page', example: 10 })
    @ApiQuery({ name: 'league', required: false, type: Number, description: 'League ID', example: 39 })
    @ApiQuery({ name: 'season', required: false, type: Number, description: 'Season year', example: 2024 })
    @ApiQuery({ name: 'country', required: false, type: String, description: 'Country name', example: 'England' })
    @ApiQuery({ name: 'search', required: false, type: String, description: 'Search teams by name, country, or code (case-insensitive)', example: 'Manchester' })
    @ApiQuery({
        name: 'newdb',
        required: false,
        type: Boolean,
        description: 'Force fetch from API and update database',
        example: true
    })
    @ApiResponse({
        status: 200,
        description: 'Teams retrieved successfully',
        example: {
            data: [
                {
                    id: 1,
                    externalId: 33,
                    name: 'Manchester United',
                    code: 'MUN',
                    country: 'England',
                    founded: 1878,
                    isManual: false,
                    logo: 'https://media.api-sports.io/football/teams/33.png'
                },
                {
                    id: 1001,
                    externalId: null,
                    name: 'Local FC',
                    code: 'LFC',
                    country: 'Vietnam',
                    founded: 2020,
                    isManual: true,
                    description: 'Local football club'
                }
            ],
            meta: { totalItems: 20, totalPages: 2, currentPage: 1, limit: 10 }
        }
    })
    @Public()
    @Get()
    async getTeams(@Query() query: GetTeamsDto): Promise<PaginatedTeamsResponse> {
        return this.teamService.getTeams(query);
    }



    @ApiOperation({
        summary: 'Get Team Leagues and Seasons (Public)',
        description: `
        Retrieve all leagues and seasons that a team has participated in.

        **🔓 PUBLIC ACCESS:**
        This endpoint is publicly accessible and does not require authentication.

        **Features:**
        - Complete participation history
        - League and season information
        - Flexible response formats
        - Smart caching for performance
        - No authentication required

        **Parameter:**
        - externalId: Team external ID (positive integer)

        **Query Parameters:**
        - newdb: Force refresh from API (optional, default: false)
        - includeHistorical: Include historical data (optional, default: true)
        - currentSeasonOnly: Only current season (optional, default: false)
        - format: Response format - 'by-league' or 'by-season' (optional, default: 'by-league')

        **Examples:**
        - /2385/leagues-seasons (Jamaica - all participations)
        - /33/leagues-seasons?format=by-season (Manchester United by season)
        - /529/leagues-seasons?currentSeasonOnly=true (Barcelona current season only)
        - /127/leagues-seasons?newdb=true (Flamengo - force refresh)

        **Use Cases:**
        - Team profile pages
        - Statistics dropdown menus
        - Historical analysis
        - League participation tracking
        - Season comparison tools
        `
    })
    @ApiParam({ name: 'externalId', type: 'number', description: 'Team external ID', example: 2385 })
    @ApiQuery({
        name: 'newdb',
        required: false,
        type: Boolean,
        description: 'Force refresh from API and update database',
        example: false
    })
    @ApiQuery({
        name: 'includeHistorical',
        required: false,
        type: Boolean,
        description: 'Include historical participation data',
        example: true
    })
    @ApiQuery({
        name: 'currentSeasonOnly',
        required: false,
        type: Boolean,
        description: 'Only return current season participations',
        example: false
    })
    @ApiQuery({
        name: 'format',
        required: false,
        enum: ['by-league', 'by-season'],
        description: 'Response format - group by league or by season',
        example: 'by-league'
    })
    @ApiResponse({
        status: 200,
        description: 'Team leagues and seasons retrieved successfully',
        example: {
            team: {
                id: 1,
                externalId: 2385,
                name: 'Jamaica',
                country: 'jamaica',
                logo: 'public/images/teams/2385.png'
            },
            participations: [
                {
                    league: {
                        id: 1,
                        externalId: 16,
                        name: 'CONCACAF Nations League',
                        country: 'World',
                        type: 'League'
                    },
                    seasons: [2024, 2022, 2019],
                    isCurrentlyActive: true
                }
            ],
            totalLeagues: 2,
            totalSeasons: 5,
            currentSeason: 2024,
            format: 'by-league'
        }
    })
    @ApiResponse({
        status: 404,
        description: 'Team not found',
        example: {
            message: 'Team with externalId 99999 not found',
            error: 'Not Found',
            statusCode: 404
        }
    })
    @Public()
    @Get(':externalId/leagues-seasons')
    async getTeamLeaguesAndSeasons(
        @Param('externalId') externalId: number,
        @Query() query: GetTeamLeaguesAndSeasonsDto
    ): Promise<{ data: TeamLeaguesAndSeasonsDto; status: number }> {
        const result = await this.teamService.getTeamLeaguesAndSeasons(externalId, query);
        return { data: result, status: 200 };
    }

    @ApiOperation({
        summary: 'Get Team by ID (Public)',
        description: `
        Retrieve detailed information for a specific team by external ID.

        **🔓 PUBLIC ACCESS:**
        This endpoint is publicly accessible and does not require authentication.

        **Features:**
        - Complete team profile
        - Team statistics and information
        - Logo and branding data
        - No authentication required

        **Parameter:**
        - externalId: Team external ID (positive integer)

        **Query Parameters:**
        - newdb: Force fetch from API and update database (optional, default: false)

        **Examples:**
        - /33 (Manchester United)
        - /529 (Barcelona)
        - /50 (Manchester City)
        - /2385?newdb=true (Force fetch from API)

        **Use Cases:**
        - Team profile pages
        - Mobile app team details
        - Website team information
        - Third-party integrations
        - Force refresh team data with newdb=true

        **Note:** Returns both API teams and manually added teams.
        Manual teams will have externalId = null and no statistics data.
        `
    })
    @ApiParam({ name: 'externalId', type: 'number', description: 'Team external ID', example: 33 })
    @ApiQuery({
        name: 'newdb',
        required: false,
        type: Boolean,
        description: 'Force fetch from API and update database',
        example: true
    })
    @ApiResponse({
        status: 200,
        description: 'Team retrieved successfully',
        example: {
            data: {
                id: 1,
                externalId: 33,
                name: 'Manchester United',
                code: 'MUN',
                country: 'England',
                founded: 1878,
                national: false,
                logo: 'https://media.api-sports.io/football/teams/33.png'
            },
            status: 200
        }
    })
    @ApiResponse({
        status: 400,
        description: 'Invalid team ID',
        example: {
            message: 'Invalid externalId: must be a positive integer',
            error: 'Bad Request',
            statusCode: 400
        }
    })
    @ApiResponse({
        status: 404,
        description: 'Team not found',
        example: {
            message: 'Team not found',
            error: 'Not Found',
            statusCode: 404
        }
    })
    @Public()
    @Get(':externalId')
    async getTeamById(
        @Param('externalId') externalId: number,
        @Query('newdb') newdb?: boolean
    ): Promise<{ data: TeamResponseDto; status: number }> {
        const team = await this.teamService.getTeamById(externalId, newdb);
        return { data: team, status: 200 };
    }

    // ==================== MANUAL TEAM MANAGEMENT ENDPOINTS ====================

    @ApiOperation({
        summary: 'Create Manual Team',
        description: `
        Create a new team manually (not from API Football).

        **Use Cases:**
        - Add local/amateur teams not available in API Football
        - Create custom teams for special events
        - Add historical teams not in current API data

        **Features:**
        - Full team information with venue details
        - Automatic validation and duplicate checking
        - Support for custom logos and descriptions
        - Audit trail with creator tracking

        **Important:**
        - id: Auto-generated by database (không gửi từ client)
        - externalId: System-generated (900M-999M range for manual teams)
        - Team names và codes phải unique across all teams (manual + API)
        - Manual teams không có statistics data

        **Access Control:**
        - Requires Editor+ role (Editor, Admin)
        - System authentication required
        `
    })
    @ApiBody({ type: CreateTeamDto })
    @ApiResponse({
        status: 201,
        description: 'Team created successfully',
        example: {
            data: {
                id: 1001, // Auto-generated by database
                externalId: 900000001, // System-generated (900M-999M range)
                name: 'Local FC',
                code: 'LFC',
                country: 'Vietnam',
                founded: 2020,
                national: false,
                isManual: true,
                logo: '/uploads/teams/local-fc-logo.png',
                description: 'Local football club in Ho Chi Minh City',
                createdBy: 1,
                updatedBy: 1
            },
            status: 201
        }
    })
    @ApiResponse({
        status: 409,
        description: 'Team name already exists',
        example: {
            message: 'Team with name "Local FC" already exists',
            error: 'Conflict',
            statusCode: 409
        }
    })
    @ApiBearerAuth()
    @EditorPlus()
    @Post('manual')
    async createManualTeam(
        @Body() createTeamDto: CreateTeamDto,
        @GetCurrentUser() user: SystemUser
    ): Promise<{ data: TeamResponseDto; status: number }> {
        const team = await this.teamService.createManualTeam(createTeamDto, user.id);
        return { data: team, status: 201 };
    }

    @ApiOperation({
        summary: 'Update Manual Team',
        description: `
        Update an existing manual team.

        **Important:**
        - Only manual teams can be updated (not API-sourced teams)
        - Partial updates supported (only send fields to update)
        - Name uniqueness is enforced

        **Access Control:**
        - Requires Editor+ role (Editor, Admin)
        - System authentication required
        `
    })
    @ApiParam({ name: 'id', type: 'number', description: 'Team ID', example: 1001 })
    @ApiBody({ type: UpdateTeamDto })
    @ApiResponse({
        status: 200,
        description: 'Team updated successfully',
        example: {
            data: {
                id: 1001,
                externalId: null,
                name: 'Local FC Updated',
                code: 'LFC',
                country: 'Vietnam',
                founded: 2020,
                national: false
            },
            status: 200
        }
    })
    @ApiResponse({
        status: 400,
        description: 'Cannot update API-sourced team',
        example: {
            message: 'Cannot update API-sourced teams. Only manual teams can be updated.',
            error: 'Bad Request',
            statusCode: 400
        }
    })
    @ApiResponse({
        status: 404,
        description: 'Team not found',
        example: {
            message: 'Team with ID 1001 not found',
            error: 'Not Found',
            statusCode: 404
        }
    })
    @ApiBearerAuth()
    @EditorPlus()
    @Put('manual/:id')
    async updateManualTeam(
        @Param('id', ParseIntPipe) id: number,
        @Body() updateTeamDto: UpdateTeamDto,
        @GetCurrentUser() user: SystemUser
    ): Promise<{ data: TeamResponseDto; status: number }> {
        const team = await this.teamService.updateManualTeam(id, updateTeamDto, user.id);
        return { data: team, status: 200 };
    }

    @ApiOperation({
        summary: 'Delete Manual Team',
        description: `
        Delete a manual team permanently.

        **Important:**
        - Only manual teams can be deleted (not API-sourced teams)
        - Teams referenced in fixtures cannot be deleted
        - This action is irreversible

        **Access Control:**
        - Requires Admin role only
        - System authentication required
        `
    })
    @ApiParam({ name: 'id', type: 'number', description: 'Team ID', example: 1001 })
    @ApiResponse({
        status: 200,
        description: 'Team deleted successfully',
        example: {
            message: 'Team "Local FC" has been successfully deleted',
            status: 200
        }
    })
    @ApiResponse({
        status: 400,
        description: 'Cannot delete team with references',
        example: {
            message: 'Cannot delete team. It is referenced in 5 fixture(s).',
            error: 'Bad Request',
            statusCode: 400
        }
    })
    @ApiResponse({
        status: 404,
        description: 'Team not found',
        example: {
            message: 'Team with ID 1001 not found',
            error: 'Not Found',
            statusCode: 404
        }
    })
    @ApiBearerAuth()
    @AdminOnly()
    @Delete('manual/:id')
    async deleteManualTeam(
        @Param('id', ParseIntPipe) id: number
    ): Promise<{ message: string; status: number }> {
        const result = await this.teamService.deleteManualTeam(id);
        return { ...result, status: 200 };
    }


}