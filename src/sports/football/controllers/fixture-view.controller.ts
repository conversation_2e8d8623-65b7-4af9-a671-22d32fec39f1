import { Controller, Post, Get, Param, Body, Query, Req, Headers, BadRequestException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiParam, ApiBody, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { FixtureViewService, ViewTrackingResult, ViewStats } from '../services/fixture-view.service';
import { Public } from '../../../auth/core/decorators/auth.decorators';

@ApiTags('Football - Fixture Views')
@Controller('football/fixtures-view')
export class FixtureViewController {
  constructor(
    private readonly fixtureViewService: FixtureViewService
  ) {}

  /**
   * Get top viewed fixtures
   */
  @Get('trending')
  @Public()
  @ApiOperation({
    summary: 'Get Trending Fixtures',
    description: `
    **Get most viewed fixtures by timeframe**
    
    Returns fixtures sorted by view count within the specified timeframe.
    Useful for displaying popular/trending fixtures to users.
    `
  })
  @ApiQuery({
    name: 'timeframe',
    required: false,
    enum: ['today', 'week', 'month', 'all'],
    description: 'Timeframe for trending calculation',
    example: 'today'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Maximum number of fixtures to return (1-50)',
    example: 10
  })
  @ApiResponse({
    status: 200,
    description: 'Trending fixtures retrieved successfully',
    example: {
      data: [{
        id: 1,
        externalId: 1145509,
        homeTeamName: 'Germany',
        awayTeamName: 'Scotland',
        viewCount: 125,
        isHot: true,
        timeframeViews: 45
      }],
      timeframe: 'today',
      limit: 10
    }
  })
  async getTrendingFixtures(
    @Query('timeframe') timeframe: 'today' | 'week' | 'month' | 'all' = 'today',
    @Query('limit') limit: string = '10'
  ) {
    // Validate limit
    const parsedLimit = parseInt(limit, 10);
    const validLimit = Math.min(Math.max(1, isNaN(parsedLimit) ? 10 : parsedLimit), 50);
    
    const data = await this.fixtureViewService.getTopViewedFixtures(timeframe, validLimit);
    
    return {
      data,
      timeframe,
      limit: validLimit
    };
  }

  /**
   * Track fixture view (Public endpoint)
   */
  @Post(':id/view')
  @Public()
  @ApiOperation({
    summary: 'Track Fixture View',
    description: `
    **Track a view for a fixture and automatically update HOT status**
    
    Features:
    - Duplicate view prevention (1 per user per day, max 1 per IP per day)
    - Automatic HOT status update when threshold reached
    - Anonymous and authenticated user support
    - Real-time view count updates
    
    Use Cases:
    - Track page views on fixture detail pages
    - Monitor fixture popularity
    - Auto-promote popular fixtures to HOT status
    - Analytics and trending calculations
    `
  })
  @ApiParam({
    name: 'id',
    type: 'number',
    description: 'Fixture ID',
    example: 1274453
  })
  @ApiBody({
    description: 'Optional session information',
    required: false,
    schema: {
      type: 'object',
      properties: {
        sessionId: { type: 'string', example: 'sess_1234567890' }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'View tracked successfully',
    example: {
      success: true,
      newViewCount: 125,
      isHot: true,
      becameHot: true,
      message: 'View tracked successfully. Fixture became HOT!'
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Duplicate view (not counted)',
    example: {
      success: false,
      newViewCount: 124,
      isHot: true,
      becameHot: false,
      message: 'Duplicate view detected'
    }
  })
  async trackView(
    @Param('id') fixtureId: string,
    @Body() body: { sessionId?: string },
    @Req() request: any,
    @Headers('user-agent') userAgent: string
  ): Promise<ViewTrackingResult & { message: string }> {
    const id = parseInt(fixtureId, 10);
    if (isNaN(id) || id <= 0) {
      throw new BadRequestException('Invalid fixture ID: must be a positive integer');
    }

    // Extract user info from JWT if available
    const userId = request.user?.id || null;
    const ipAddress = request.ip || request.connection.remoteAddress || '127.0.0.1';

    const result = await this.fixtureViewService.trackView(id, {
      userId,
      ipAddress,
      userAgent,
      sessionId: body?.sessionId
    });

    return {
      ...result,
      message: result.success 
        ? (result.becameHot ? 'View tracked successfully. Fixture became HOT!' : 'View tracked successfully')
        : result.reason || 'View tracking failed'
    };
  }

  /**
   * Get fixture view statistics
   */
  @Get(':id/view-stats')
  @Public()
  @ApiOperation({
    summary: 'Get Fixture View Statistics',
    description: `
    **Get detailed view statistics for a fixture**
    
    Returns:
    - Total view count
    - Today's view count  
    - HOT status and since when
    - HOT threshold
    - Views needed to become HOT
    `
  })
  @ApiParam({
    name: 'id',
    type: 'number',
    description: 'Fixture ID',
    example: 1274453
  })
  @ApiResponse({
    status: 200,
    description: 'View statistics retrieved successfully',
    example: {
      totalViews: 125,
      todayViews: 23,
      isHot: true,
      hotSince: '2025-06-27T10:30:00Z',
      threshold: 100,
      viewsToHot: 0
    }
  })
  async getViewStats(@Param('id') fixtureId: string): Promise<ViewStats> {
    const id = parseInt(fixtureId, 10);
    if (isNaN(id) || id <= 0) {
      throw new BadRequestException('Invalid fixture ID: must be a positive integer');
    }

    const stats = await this.fixtureViewService.getFixtureViewStats(id);
    
    if (!stats) {
      throw new BadRequestException('Fixture not found');
    }

    return stats;
  }
}