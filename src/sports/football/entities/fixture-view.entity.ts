import { Entity, PrimaryGeneratedColumn, Column, Index, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Fixture } from '../models/fixture.entity';

@Entity('fixture_views')
@Index(['fixtureId', 'userId', 'viewDate'], { unique: true, where: '"userId" IS NOT NULL' })
@Index(['fixtureId', 'ipAddress', 'viewDate'], { unique: true })
export class FixtureView {
    @PrimaryGeneratedColumn()
    id: number;

    @Index()
    @Column()
    fixtureId: number;

    @Column({ nullable: true })
    @Index()
    userId: number; // null for anonymous users

    @Column({ type: 'inet' })
    @Index()
    ipAddress: string;

    @Column({ type: 'text', nullable: true })
    userAgent: string;

    @Column({ nullable: true, length: 100 })
    sessionId: string;

    @Column({ type: 'date', default: () => 'CURRENT_DATE' })
    @Index()
    viewDate: Date; // For daily uniqueness constraint

    @CreateDateColumn()
    @Index()
    createdAt: Date;

    @ManyToOne(() => Fixture, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'fixtureId' })
    fixture: Fixture;
}
