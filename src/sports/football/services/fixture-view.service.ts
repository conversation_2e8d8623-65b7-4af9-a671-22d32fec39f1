import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, QueryRunner, DataSource } from 'typeorm';
import { FixtureView } from '../entities/fixture-view.entity';
import { Fixture } from '../models/fixture.entity';
import { CacheService } from '../../../core/cache/cache.service';
import { ConfigService } from '@nestjs/config';

export interface ViewTrackingResult {
  success: boolean;
  newViewCount: number;
  isHot: boolean;
  becameHot: boolean;
  reason?: string;
}

export interface ViewStats {
  totalViews: number;
  todayViews: number;
  isHot: boolean;
  hotSince?: Date;
  threshold: number;
  viewsToHot: number;
}

@Injectable()
export class FixtureViewService {
  private readonly logger = new Logger(FixtureViewService.name);
  private readonly HOT_THRESHOLD: number;

  constructor(
    @InjectRepository(FixtureView)
    private readonly viewRepository: Repository<FixtureView>,
    @InjectRepository(Fixture)
    private readonly fixtureRepository: Repository<Fixture>,
    private readonly cacheService: CacheService,
    private readonly configService: ConfigService,
    private readonly dataSource: DataSource,
  ) {
    this.HOT_THRESHOLD = this.configService.get<number>('FIXTURE_HOT_THRESHOLD', 100);
  }

  /**
   * Track a view for a fixture
   */
  async trackView(
    fixtureExternalId: number,
    viewData: {
      userId?: number;
      ipAddress: string;
      userAgent?: string;
      sessionId?: string;
    }
  ): Promise<ViewTrackingResult> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Check if fixture exists using externalId
      const fixture = await queryRunner.manager.findOne(Fixture, {
        where: { externalId: fixtureExternalId }
      });

      if (!fixture) {
        await queryRunner.rollbackTransaction();
        return {
          success: false,
          newViewCount: 0,
          isHot: false,
          becameHot: false,
          reason: 'Fixture not found'
        };
      }

      // Check for duplicate view using internal fixtureId
      const today = new Date().toISOString().split('T')[0];
      const existingView = await this.checkDuplicateView(
        queryRunner,
        fixture.id,
        viewData.userId,
        viewData.ipAddress,
        today
      );

      if (existingView) {
        await queryRunner.rollbackTransaction();
        return {
          success: false,
          newViewCount: fixture.viewCount || 0,
          isHot: fixture.isHot,
          becameHot: false,
          reason: 'Duplicate view detected'
        };
      }

      // Create view record
      const view = queryRunner.manager.create(FixtureView, {
        fixtureId: fixture.id,
        userId: viewData.userId,
        ipAddress: viewData.ipAddress,
        userAgent: viewData.userAgent,
        sessionId: viewData.sessionId,
        viewDate: new Date(today),
      });

      await queryRunner.manager.save(view);

      // Update fixture view count
      const newViewCount = (fixture.viewCount || 0) + 1;
      const wasHot = fixture.isHot;
      const becameHot = !wasHot && newViewCount >= this.HOT_THRESHOLD;

      await queryRunner.manager.update(Fixture, fixture.id, {
        viewCount: newViewCount,
        isHot: becameHot || wasHot,
        hotSince: becameHot ? new Date() : fixture.hotSince,
      });

      await queryRunner.commitTransaction();

      // Clear cache
      await this.clearFixtureViewCache(fixture.id);

      this.logger.log(
        `View tracked: Fixture ${fixture.id} (${fixtureExternalId}), User ${viewData.userId || 'anonymous'}, ` +
        `New count: ${newViewCount}, Became hot: ${becameHot}`
      );

      return {
        success: true,
        newViewCount,
        isHot: becameHot || wasHot,
        becameHot,
      };

    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Failed to track view for fixture ${fixtureExternalId}:`, error);
      
      return {
        success: false,
        newViewCount: 0,
        isHot: false,
        becameHot: false,
        reason: 'Database error'
      };
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Get view statistics for a fixture
   */
  async getFixtureViewStats(fixtureExternalId: number): Promise<ViewStats | null> {
    const cacheKey = `fixture_view_stats_${fixtureExternalId}`;
    const cached = await this.cacheService.getCache(cacheKey);

    if (cached) {
      return JSON.parse(cached);
    }

    const fixture = await this.fixtureRepository.findOne({
      where: { externalId: fixtureExternalId }
    });

    if (!fixture) {
      return null;
    }

    // Get today's views using internal fixtureId
    const today = new Date().toISOString().split('T')[0];
    const todayViews = await this.viewRepository.count({
      where: {
        fixtureId: fixture.id,
        viewDate: new Date(today)
      }
    });

    const totalViews = fixture.viewCount || 0;
    const isHot = fixture.isHot;
    const viewsToHot = Math.max(0, this.HOT_THRESHOLD - totalViews);

    const stats: ViewStats = {
      totalViews,
      todayViews,
      isHot,
      hotSince: fixture.hotSince,
      threshold: this.HOT_THRESHOLD,
      viewsToHot,
    };

    // Cache for 5 minutes
    await this.cacheService.setCache(cacheKey, JSON.stringify(stats), 300);

    return stats;
  }

  /**
   * Get top viewed fixtures by timeframe
   */
  async getTopViewedFixtures(
    timeframe: 'today' | 'week' | 'month' | 'all',
    limit: number = 10
  ): Promise<any[]> {
    const cacheKey = `top_viewed_fixtures_${timeframe}_${limit}`;
    const cached = await this.cacheService.getCache(cacheKey);

    if (cached) {
      return JSON.parse(cached);
    }

    let dateFilter = '';
    const now = new Date();

    switch (timeframe) {
      case 'today':
        const today = now.toISOString().split('T')[0];
        dateFilter = `AND fv."viewDate" = '${today}'`;
        break;
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        dateFilter = `AND fv."viewDate" >= '${weekAgo.toISOString().split('T')[0]}'`;
        break;
      case 'month':
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        dateFilter = `AND fv."viewDate" >= '${monthAgo.toISOString().split('T')[0]}'`;
        break;
      case 'all':
      default:
        dateFilter = '';
    }

    const query = `
      SELECT 
        f.id,
        f."externalId" as "externalId",
        f."leagueName" as "leagueName",
        f."homeTeamId" as "homeTeamId",
        f."awayTeamId" as "awayTeamId",
        f.date,
        f."viewCount" as "viewCount",
        f."isHot" as "isHot",
        f."hotSince" as "hotSince",
        f.data,
        COUNT(fv.id) as "timeframeViews"
      FROM fixtures f
      LEFT JOIN fixture_views fv ON f.id = fv."fixtureId" ${dateFilter}
      WHERE f."viewCount" > 0
      GROUP BY f.id
      ORDER BY 
        ${timeframe === 'all' ? 'f."viewCount"' : 'COUNT(fv.id)'} DESC,
        f.date ASC
      LIMIT $1
    `;

    const results = await this.dataSource.query(query, [limit]);

    // Transform results to proper format
    const fixtures = results.map((row: any) => ({
      id: row.id,
      externalId: row.externalId,
      leagueName: row.leagueName,
      homeTeamId: row.homeTeamId,
      awayTeamId: row.awayTeamId,
      homeTeamName: row.data?.homeTeamName || 'Unknown',
      awayTeamName: row.data?.awayTeamName || 'Unknown',
      date: row.date,
      viewCount: parseInt(row.viewCount),
      isHot: row.isHot,
      hotSince: row.hotSince,
      timeframeViews: parseInt(row.timeframeViews),
      status: row.data?.status,
    }));

    // Cache for 10 minutes
    await this.cacheService.setCache(cacheKey, JSON.stringify(fixtures), 600);

    return fixtures;
  }

  /**
   * Check for duplicate view
   */
  private async checkDuplicateView(
    queryRunner: QueryRunner,
    fixtureId: number,
    userId: number | undefined,
    ipAddress: string,
    viewDate: string
  ): Promise<boolean> {
    if (userId) {
      // Check for authenticated user
      const userView = await queryRunner.manager.findOne(FixtureView, {
        where: {
          fixtureId,
          userId,
          viewDate: new Date(viewDate)
        }
      });
      return !!userView;
    } else {
      // Check for anonymous user by IP
      const ipView = await queryRunner.manager.findOne(FixtureView, {
        where: {
          fixtureId,
          ipAddress,
          viewDate: new Date(viewDate)
        }
      });
      return !!ipView;
    }
  }

  /**
   * Clear fixture view cache
   */
  private async clearFixtureViewCache(fixtureId: number): Promise<void> {
    const cacheKeys = [
      `fixture_view_stats_${fixtureId}`,
      'top_viewed_fixtures_today_10',
      'top_viewed_fixtures_week_10',
      'top_viewed_fixtures_month_10',
      'top_viewed_fixtures_all_10',
    ];

    await Promise.all(
      cacheKeys.map(key => this.cacheService.deleteByPattern(key))
    );

    // Clear trending fixtures cache patterns
    await this.cacheService.deleteByPattern('top_viewed_fixtures_*');
  }
}
