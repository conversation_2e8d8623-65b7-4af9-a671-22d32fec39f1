import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import axios from 'axios';
import { FixtureStatistics } from '../models/fixture-statistics.entity';
import { FixtureStatisticsDto } from '../models/fixture-statistics.dto';
import { Fixture } from '../models/fixture.entity';
import { League } from '../models/league.entity';
import { GetFixturesDto, PaginatedFixturesResponse } from '../models/fixture.dto';
import { FixtureService } from './fixture.service';
import { CacheService } from '../../../core';
import { UtilsService, ImageService } from '../../../shared';

@Injectable()
export class FixtureStatisticsService {
    private readonly logger = new Logger(FixtureStatisticsService.name);

    constructor(
        @InjectRepository(FixtureStatistics)
        private readonly statsRepository: Repository<FixtureStatistics>,
        @InjectRepository(Fixture)
        private readonly fixtureRepository: Repository<Fixture>,
        @InjectRepository(League)
        private readonly leagueRepository: Repository<League>,
        private readonly fixtureService: FixtureService,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
        private readonly utilsService: UtilsService,
        private readonly imageService: ImageService,
    ) { }

    /**
     * 🔥 Get active league IDs - đơn giản
     * @returns Array of active league external IDs
     */
    private async getActiveLeagueIds(): Promise<number[]> {
        const activeLeagues = await this.leagueRepository.find({
            where: { active: true },
        });
        return activeLeagues.map(league => league.externalId);
    }

    /**
     * Get statistics for a fixture by external ID
     * @param externalId - Fixture external ID
     * @returns Fixture statistics
     */
    async getStatistics(externalId: number): Promise<{ data: FixtureStatisticsDto[]; status: number; message?: string }> {
        const cacheKey = `fixture_stats_${externalId}`;
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Returning statistics from cache for key: ${cacheKey}`);
            return { data: JSON.parse(cached), status: 200 };
        }

        let stats = await this.statsRepository.find({ where: { fixtureId: externalId } });
        if (stats.length === 0) {
            this.logger.debug(`No statistics found in DB for fixture ${externalId}, fetching from API`);
            stats = await this.fetchFromApi(externalId);
            if (stats.length > 0) {
                await this.statsRepository.save(stats);
                this.logger.debug(`Saved ${stats.length} statistics to DB`);
            }
        }

        const response = stats.map(stat => ({
            fixtureId: stat.fixtureId,
            teamName: stat.teamName,
            teamLogo: stat.teamLogo,
            statistics: stat.statistics,
        }));

        if (response.length === 0) {
            this.logger.warn(`No statistics available for fixture ${externalId}`);
            return { data: [], status: 200, message: `No statistics available for fixture ${externalId}` };
        }

        await this.cacheService.setCache(cacheKey, JSON.stringify(response), 3600);
        return { data: response, status: 200 };
    }

    /**
     * Get upcoming and live fixtures with smart caching and time-based filtering
     * 🔥 ENHANCED: Now includes active league filtering
     * @param query - Query parameters (league, season, team, date, page, limit)
     * @returns Paginated list of upcoming and live fixtures from active leagues only
     */
    async getUpcomingAndLiveFixtures(query: GetFixturesDto): Promise<PaginatedFixturesResponse> {
        const page = query.page || 1;
        const limit = query.limit || 10;

        // ✅ SMART CACHING
        const cacheKey = `upcoming_live_fixtures_${page}_${limit}_${query.league || ''}_${query.team || ''}`;
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Returning upcoming/live fixtures from cache: ${cacheKey}`);
            return JSON.parse(cached);
        }

        // 🔥 STEP 1: Get active league IDs
        const activeLeagueIds = await this.getActiveLeagueIds();

        // 🔥 STEP 2: Early return nếu không có active leagues
        if (activeLeagueIds.length === 0) {
            this.logger.debug('No active leagues found for upcoming/live fixtures');
            const emptyResponse: PaginatedFixturesResponse = {
                data: [],
                meta: { totalItems: 0, totalPages: 0, currentPage: page, limit },
                status: 200,
            };
            // Cache empty response để avoid repeated queries
            await this.cacheService.setCache(cacheKey, JSON.stringify(emptyResponse), 10);
            return emptyResponse;
        }

        // ✅ SMART TIME-BASED FILTERING FOR LIVE FIXTURES
        const now = this.utilsService.getUtcNow();
        const windowStart = new Date(now.getTime() - 4 * 60 * 60 * 1000); // 4 hours ago (for ongoing matches)
        const windowEnd = new Date(now.getTime() + 2 * 60 * 60 * 1000); // 2 hours ahead

        const qb = this.fixtureRepository.createQueryBuilder('fixture')
            .where('fixture.date BETWEEN :windowStart AND :windowEnd', { windowStart, windowEnd })
            .andWhere('fixture.leagueId IN (:...activeLeagueIds)', { activeLeagueIds }) // 🔥 THÊM ACTIVE LEAGUE FILTER
            .andWhere('fixture.data->>\'status\' IN (:...statuses)', {
                statuses: ['NS', '1H', 'HT', '2H', 'ET', 'BT', 'P', 'LIVE'], // Keep NS, let cronjob handle status updates
            });

        // Optional filters
        if (query.league) {
            qb.andWhere('fixture.leagueId = :league', { league: query.league });
        }
        if (query.team) {
            qb.andWhere('fixture.homeTeamId = :team OR fixture.awayTeamId = :team', { team: query.team });
        }

        qb.orderBy('fixture.date', 'ASC')
            .skip((page - 1) * limit)
            .take(limit);

        const [fixtures, totalItems] = await qb.getManyAndCount();

        // ✅ SMART STATUS CLASSIFICATION
        const classifiedFixtures = fixtures.map(fixture => {
            const fixtureTime = new Date(fixture.date);
            const timeDiff = fixtureTime.getTime() - now.getTime();
            const minutesUntilStart = Math.floor(timeDiff / (1000 * 60));

            let computedStatus: 'UPCOMING' | 'LIVE';

            if (minutesUntilStart > 5) {
                computedStatus = 'UPCOMING';
            } else if (minutesUntilStart <= 5 && minutesUntilStart >= -10) {
                computedStatus = 'LIVE';
            } else {
                // Use API status for ongoing matches
                const apiStatus = fixture.data.status;
                computedStatus = ['1H', 'HT', '2H', 'ET', 'BT', 'P', 'LIVE'].includes(apiStatus)
                    ? 'LIVE'
                    : 'UPCOMING';
            }

            const mappedFixture = this.fixtureService.mapToResponseDto([fixture])[0];
            return {
                ...mappedFixture,
                computedStatus,
                minutesUntilStart: minutesUntilStart > 0 ? minutesUntilStart : null
            };
        });

        const response: PaginatedFixturesResponse = {
            data: classifiedFixtures,
            meta: {
                totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                limit,
            },
            status: 200,
        };

        // ✅ CACHE FOR 10 SECONDS (real-time data) - Cache even empty results
        await this.cacheService.setCache(cacheKey, JSON.stringify(response), 10);
        this.logger.debug(`Cached upcoming/live fixtures (active leagues only): ${cacheKey} (TTL: 10s, items: ${response.data.length}, active leagues: ${activeLeagueIds.length})`);

        return response;
    }

    /**
     * Fetch statistics from external API with retry
     * @param externalId - Fixture external ID
     * @returns List of statistics
     */
    private async fetchFromApi(externalId: number): Promise<FixtureStatistics[]> {
        try {
            const response = await this.executeWithRetry(async () => {
                return axios.get(`${this.configService.get('app.apiFootballUrl')}/fixtures/statistics`, {
                    params: { fixture: externalId },
                    headers: { 'x-apisports-key': this.configService.get('app.apiFootballKey') },
                });
            });

            if (response.data.errors && Object.keys(response.data.errors).length > 0) {
                this.logger.error(`API returned errors for fixture ${externalId}: ${JSON.stringify(response.data.errors)}`);
                return [];
            }

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No statistics data returned from API for fixture ${externalId}`);
                return [];
            }

            // Process statistics with image handling
            const processedStats = await Promise.all(response.data.response.map(async (data: any) => {
                // Download team logo if available
                let teamLogo = data.team.logo;
                if (teamLogo) {
                    try {
                        teamLogo = await this.imageService.downloadImage(
                            data.team.logo,
                            'teams',
                            `${data.team.id}.png`
                        );
                    } catch (error) {
                        this.logger.warn(`Failed to download team logo for team ${data.team.id}: ${error.message}`);
                        teamLogo = data.team.logo; // Keep original URL as fallback
                    }
                }

                const stat = new FixtureStatistics();
                stat.fixtureId = externalId;
                stat.teamName = data.team.name;
                stat.teamLogo = teamLogo; // Add team logo to statistics
                stat.statistics = {
                    shotsOnGoal: data.statistics.find((s: any) => s.type === 'Shots on Goal')?.value || 0,
                    shotsOffGoal: data.statistics.find((s: any) => s.type === 'Shots off Goal')?.value || 0,
                    totalShots: data.statistics.find((s: any) => s.type === 'Total Shots')?.value || 0,
                    corners: data.statistics.find((s: any) => s.type === 'Corner Kicks')?.value || 0,
                    offsides: data.statistics.find((s: any) => s.type === 'Offsides')?.value || 0,
                    yellowCards: data.statistics.find((s: any) => s.type === 'Yellow Cards')?.value || 0,
                    redCards: data.statistics.find((s: any) => s.type === 'Red Cards')?.value || 0,
                    possession: data.statistics.find((s: any) => s.type === 'Ball Possession')?.value || '0%',
                };
                return stat;
            }));

            return processedStats;
        } catch (error) {
            this.logger.error(`Failed to fetch statistics from API for fixture ${externalId}: ${error.message}`);
            return [];
        }
    }

    /**
     * Execute a function with retry logic
     * @param fn - Function to execute
     * @param retries - Number of retries
     * @param delay - Delay between retries (ms)
     * @returns Result of the function
     */
    private async executeWithRetry<T>(fn: () => Promise<T>, retries = 3, delay = 1000): Promise<T> {
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                return await fn();
            } catch (error) {
                if (attempt === retries) {
                    this.logger.error(`Failed after ${retries} attempts: ${error.message}`);
                    throw error;
                }
                this.logger.warn(`Attempt ${attempt} failed: ${error.message}. Retrying after ${delay}ms...`);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
        throw new Error('Unexpected error in retry logic');
    }
}