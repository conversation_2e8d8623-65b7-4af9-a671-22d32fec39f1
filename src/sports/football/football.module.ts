import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UsersModule } from '../../auth/users/users.module';
import { FixtureService } from './services/fixture.service';
import { FixtureStatisticsService } from './services/fixture-statistics.service';
import { FixtureEventsService } from './services/fixture-events.service';
import { FixturePlayersService } from './services/fixture-players.service';
import { FixtureViewService } from './services/fixture-view.service';
import { LeagueService } from './services/league.service';
import { TeamService } from './services/team.service';
import { TeamStatisticsService } from './services/team-statistics.service';
import { PlayerService } from './services/player.service';
import { StandingService } from './services/standing.service';
import { SeasonSyncModule } from './season-sync.module';
import { Fixture } from './models/fixture.entity';
import { League } from './models/league.entity';
import { Team } from './models/team.entity';
import { FixtureStatistics } from './models/fixture-statistics.entity';
import { FixtureEvents } from './models/fixture-events.entity';
import { FixtureLineup } from './models/fixture-players.entity';
import { TeamStatistics } from './models/team-statistics.entity';
import { Player } from './models/player.entity';
import { PlayerStatistics } from './models/player-statistics.entity';
import { Standing } from './models/standing.entity';
import { FixtureView } from './entities/fixture-view.entity';


// Base Football Module - Contains shared services and entities
@Module({
  imports: [
    TypeOrmModule.forFeature([Fixture, League, Team, FixtureStatistics, FixtureEvents, FixtureLineup, TeamStatistics, Player, PlayerStatistics, Standing, FixtureView]),
    SeasonSyncModule,
    UsersModule,
  ],
  providers: [
    FixtureService,
    LeagueService,
    TeamService,
    FixtureStatisticsService,
    FixtureEventsService,
    FixturePlayersService,
    FixtureViewService,
    TeamStatisticsService,
    PlayerService,
    StandingService,
  ],
  exports: [
    FixtureService,
    LeagueService,
    TeamService,
    FixtureStatisticsService,
    FixtureEventsService,
    FixturePlayersService,
    FixtureViewService,
    TeamStatisticsService,
    PlayerService,
    StandingService,
    SeasonSyncModule,
    TypeOrmModule,
  ],
})
export class FootballModule { }