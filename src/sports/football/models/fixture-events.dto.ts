import { IsInt, <PERSON>Array, IsObject, IsString, IsOptional, IsNumber } from 'class-validator';

export class EventDto {
    @IsObject()
    time: {
        elapsed: number;
        extra?: number;
    };

    @IsObject()
    team: {
        id: number;
        name: string;
        logo: string;
    };

    @IsObject()
    player: {
        id: number;
        name: string;
    };

    @IsObject()
    @IsOptional()
    assist?: {
        id?: number;
        name?: string;
    };

    @IsString()
    type: string;

    @IsString()
    detail: string;
}

export class FixtureEventsDto {
    @IsInt()
    fixtureId: number;

    @IsArray()
    events: EventDto[];
}
