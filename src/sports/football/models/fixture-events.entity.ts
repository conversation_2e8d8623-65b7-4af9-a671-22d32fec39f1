import { En<PERSON><PERSON>, Column, PrimaryGeneratedColumn } from 'typeorm';

interface EventData {
    time: {
        elapsed: number;
        extra?: number;
    };
    team: {
        id: number;
        name: string;
        logo: string;
    };
    player: {
        id: number;
        name: string;
    };
    assist?: {
        id?: number;
        name?: string;
    };
    type: string;
    detail: string;
}

@Entity('fixture_events')
export class FixtureEvents {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    fixtureId: number;

    @Column({ type: 'jsonb' })
    events: EventData[];

    @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
    updatedAt: Date;
}
