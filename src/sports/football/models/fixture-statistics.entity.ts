import { Entity, Column, PrimaryGeneratedColumn } from 'typeorm';

interface StatisticsData {
    shotsOnGoal?: number;
    shotsOffGoal?: number;
    totalShots?: number;
    corners?: number;
    offsides?: number;
    yellowCards?: number;
    redCards?: number;
    possession?: string;
}

@Entity('fixture_statistics')
export class FixtureStatistics {
    @PrimaryGeneratedColumn()
    id: number;

    @Column()
    fixtureId: number;

    @Column()
    teamName: string;

    @Column({ nullable: true })
    teamLogo?: string;

    @Column({ type: 'jsonb' })
    statistics: StatisticsData;

    @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP' })
    createdAt: Date;

    @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
    updatedAt: Date;
}