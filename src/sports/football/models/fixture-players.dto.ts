import { IsInt, <PERSON><PERSON><PERSON>y, IsObject, IsString, IsO<PERSON>al, IsN<PERSON>ber, IsBoolean } from 'class-validator';

export class PlayerDto {
    @IsNumber()
    id: number;

    @IsString()
    name: string;

    @IsNumber()
    @IsOptional()
    number?: number;

    @IsString()
    @IsOptional()
    position?: string;

    @IsString()
    @IsOptional()
    grid?: string;
}

export class CoachDto {
    @IsNumber()
    id: number;

    @IsString()
    name: string;

    @IsString()
    photo: string;
}

export class TeamColorsDto {
    @IsObject()
    @IsOptional()
    player?: {
        primary: string;
        number: string;
        border: string;
    };

    @IsObject()
    @IsOptional()
    goalkeeper?: {
        primary: string;
        number: string;
        border: string;
    };
}

export class LineupDto {
    @IsObject()
    team: {
        id: number;
        name: string;
        logo: string;
        colors?: TeamColorsDto;
    };

    @IsString()
    @IsOptional()
    formation?: string;

    @IsArray()
    startXI: PlayerDto[];

    @IsArray()
    substitutes: PlayerDto[];

    @IsObject()
    @IsOptional()
    coach?: CoachDto;
}

export class FixturePlayersDto {
    @IsInt()
    fixtureId: number;

    @IsArray()
    lineups: LineupDto[];
}
