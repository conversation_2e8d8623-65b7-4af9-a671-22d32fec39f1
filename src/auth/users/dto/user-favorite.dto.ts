import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Is<PERSON>tring, IsBoolean, IsO<PERSON>al, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { FavoriteEntityType } from '../entities/user-favorite.entity';

export class CreateUserFavoriteDto {
  @ApiProperty({
    description: 'Type of entity to add to favorites (team, league, or player)',
    enum: FavoriteEntityType,
    example: FavoriteEntityType.TEAM,
    enumName: 'FavoriteEntityType'
  })
  @IsEnum(FavoriteEntityType)
  entityType: FavoriteEntityType;

  @ApiProperty({
    description: 'External ID from API-Sports.io (e.g., Manchester United = 33, Premier League = 39, Messi = 276)',
    example: 33,
    minimum: 1,
    type: 'number'
  })
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  entityId: number;

  @ApiPropertyOptional({
    description: 'Display name for the entity (cached for quick UI display). If not provided, will be fetched automatically.',
    example: 'Manchester United',
    maxLength: 255
  })
  @IsOptional()
  @IsString()
  entityName?: string;

  @ApiPropertyOptional({
    description: 'Logo URL for the entity (cached for quick UI display). If not provided, will use API-Sports.io default.',
    example: 'https://media.api-sports.io/football/teams/33.png',
    maxLength: 500
  })
  @IsOptional()
  @IsString()
  entityLogo?: string;

  @ApiPropertyOptional({
    description: 'Enable push notifications for upcoming fixtures/matches involving this entity (recommended for favorite teams)',
    default: true,
    example: true
  })
  @IsOptional()
  @IsBoolean()
  notifyFixtures?: boolean;

  @ApiPropertyOptional({
    description: 'Enable push notifications for news and articles about this entity (good for leagues/important teams)',
    default: false,
    example: false
  })
  @IsOptional()
  @IsBoolean()
  notifyNews?: boolean;

  @ApiPropertyOptional({
    description: 'Enable push notifications for player transfers involving this entity (use sparingly to avoid spam)',
    default: false,
    example: false
  })
  @IsOptional()
  @IsBoolean()
  notifyTransfers?: boolean;
}

export class UpdateUserFavoriteDto {
  @ApiPropertyOptional({
    description: 'Update the cached display name for this entity (useful if team/league name changes)',
    example: 'Manchester United FC',
    maxLength: 255
  })
  @IsOptional()
  @IsString()
  entityName?: string;

  @ApiPropertyOptional({
    description: 'Update the cached logo URL for this entity (useful if logo changes)',
    example: 'https://media.api-sports.io/football/teams/33.png',
    maxLength: 500
  })
  @IsOptional()
  @IsString()
  entityLogo?: string;

  @ApiPropertyOptional({
    description: 'Enable/disable this favorite temporarily (false = mute all notifications, true = re-enable)',
    default: true,
    example: true
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Toggle fixture notifications (upcoming matches involving this entity)',
    default: true,
    example: false
  })
  @IsOptional()
  @IsBoolean()
  notifyFixtures?: boolean;

  @ApiPropertyOptional({
    description: 'Toggle news notifications (articles and updates about this entity)',
    default: false,
    example: true
  })
  @IsOptional()
  @IsBoolean()
  notifyNews?: boolean;

  @ApiPropertyOptional({
    description: 'Toggle transfer notifications (player movements involving this entity)',
    default: false,
    example: false
  })
  @IsOptional()
  @IsBoolean()
  notifyTransfers?: boolean;
}

export class UserFavoriteResponseDto {
  @ApiProperty({
    description: 'Favorite ID',
    example: 1
  })
  id: number;

  @ApiProperty({
    description: 'User ID',
    example: 123
  })
  userId: number;

  @ApiProperty({
    description: 'Type of entity',
    enum: FavoriteEntityType,
    example: FavoriteEntityType.TEAM
  })
  entityType: FavoriteEntityType;

  @ApiProperty({
    description: 'Entity external ID',
    example: 33
  })
  entityId: number;

  @ApiProperty({
    description: 'Entity name',
    example: 'Manchester United',
    nullable: true
  })
  entityName: string | null;

  @ApiProperty({
    description: 'Entity logo URL',
    example: 'https://media.api-sports.io/football/teams/33.png',
    nullable: true
  })
  entityLogo: string | null;

  @ApiProperty({
    description: 'Whether the favorite is active',
    example: true
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Notifications enabled for fixtures',
    example: true
  })
  notifyFixtures: boolean;

  @ApiProperty({
    description: 'Notifications enabled for news',
    example: false
  })
  notifyNews: boolean;

  @ApiProperty({
    description: 'Notifications enabled for transfers',
    example: false
  })
  notifyTransfers: boolean;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00.000Z'
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00.000Z'
  })
  updatedAt: Date;
}

export class GetUserFavoritesDto {
  @ApiPropertyOptional({
    description: 'Filter favorites by entity type (team, league, player). Leave empty to get all types.',
    enum: FavoriteEntityType,
    example: 'team'
  })
  @IsOptional()
  @IsEnum(FavoriteEntityType)
  entityType?: FavoriteEntityType;

  @ApiPropertyOptional({
    description: 'Filter by active status. true = only active favorites, false = only disabled favorites, undefined = all',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Maximum number of favorites to return per request (1-100)',
    default: 50,
    minimum: 1,
    maximum: 100,
    example: 20
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Number of favorites to skip for pagination (used with limit for paging)',
    default: 0,
    minimum: 0,
    example: 0
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  offset?: number;
}

export class UserFavoriteStatsDto {
  @ApiProperty({
    description: 'Total number of favorites',
    example: 15
  })
  total: number;

  @ApiProperty({
    description: 'Number of favorite teams',
    example: 5
  })
  teams: number;

  @ApiProperty({
    description: 'Number of favorite leagues',
    example: 8
  })
  leagues: number;

  @ApiProperty({
    description: 'Number of favorite players',
    example: 2
  })
  players: number;

  @ApiProperty({
    description: 'Number of active favorites',
    example: 12
  })
  active: number;

  @ApiProperty({
    description: 'Number of favorites with notifications enabled',
    example: 10
  })
  withNotifications: number;
}
