import { Controller, Get, Post, Patch, Delete, Param, Query, Body, UseGuards, Logger } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { UserFavoriteService } from '../services/user-favorite.service';
import { CreateUserFavoriteDto, UpdateUserFavoriteDto, GetUserFavoritesDto, UserFavoriteResponseDto, UserFavoriteStatsDto } from '../dto/user-favorite.dto';
import { FavoriteEntityType } from '../entities/user-favorite.entity';
import { UserJwtAuthGuard } from '../guards/user-jwt-auth.guard';
import { CurrentUser } from '../../core/decorators/auth.decorators';
import { RegisteredUser } from '../entities/registered-user.entity';

@ApiTags('User - Favorites 💗')
@Controller('user/favorites')
@UseGuards(UserJwtAuthGuard)
@ApiBearerAuth('user-jwt')
export class UserFavoriteController {
  private readonly logger = new Logger(UserFavoriteController.name);

  constructor(
    private readonly favoriteService: UserFavoriteService,
  ) {}

  @ApiOperation({
    summary: '➕ Add Entity to Favorites',
    description: `
    **Add a team, league, or player to user's favorites list**
    
    Use this endpoint to save teams, leagues, or players that the user wants to follow.
    
    **Entity Types:**
    - \`team\`: Football teams (e.g., Manchester United, Real Madrid)
    - \`league\`: Football leagues (e.g., Premier League, La Liga)  
    - \`player\`: Football players (e.g., Messi, Ronaldo)
    
    **Entity IDs:**
    - Use the \`externalId\` from API-Sports.io
    - Example: Manchester United = 33, Premier League = 39
    
    **Notification Settings:**
    - \`notifyFixtures\`: Get notified about upcoming matches
    - \`notifyNews\`: Get notified about news and updates
    - \`notifyTransfers\`: Get notified about player transfers
    `
  })
  @ApiResponse({
    status: 201,
    description: 'Favorite added successfully',
    type: UserFavoriteResponseDto,
    example: {
      data: {
        id: 1,
        entityType: 'team',
        entityId: 33,
        entityName: 'Manchester United',
        entityLogo: 'https://media.api-sports.io/football/teams/33.png',
        isActive: true,
        notifyFixtures: true,
        notifyNews: false,
        notifyTransfers: false,
        createdAt: '2025-06-27T10:00:00Z',
        updatedAt: '2025-06-27T10:00:00Z'
      },
      status: 201,
      message: 'Favorite added successfully'
    }
  })
  @ApiResponse({
    status: 409,
    description: 'Entity already exists in user favorites',
    example: {
      message: 'team with ID 33 is already in favorites',
      error: 'Conflict',
      statusCode: 409
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request data',
    example: {
      message: ['entityType must be a valid enum value', 'entityId must be a positive number'],
      error: 'Bad Request',
      statusCode: 400
    }
  })
  @Post()
  async addFavorite(
    @CurrentUser() user: RegisteredUser,
    @Body() createDto: CreateUserFavoriteDto,
  ): Promise<{ data: UserFavoriteResponseDto; status: number; message: string }> {
    const result = await this.favoriteService.addFavorite(user.id, createDto);
    
    return {
      data: result.favorite!,
      status: 201,
      message: result.message!,
    };
  }

  @ApiOperation({
    summary: '📋 Get User Favorites List',
    description: `
    **Retrieve user's favorites with optional filtering**
    
    Returns a list of all entities (teams, leagues, players) that the user has favorited.
    
    **Query Parameters:**
    - \`entityType\`: Filter by type (\`team\`, \`league\`, \`player\`)
    - \`isActive\`: Filter by active status (\`true\`/\`false\`)
    - \`limit\`: Number of results to return (default: 50, max: 100)
    - \`offset\`: Number of results to skip for pagination (default: 0)
    
    **Use Cases:**
    - Get all user favorites: \`GET /user/favorites\`
    - Get only favorite teams: \`GET /user/favorites?entityType=team\`
    - Get active favorites only: \`GET /user/favorites?isActive=true\`
    - Pagination: \`GET /user/favorites?limit=20&offset=40\`
    `
  })
  @ApiResponse({
    status: 200,
    description: 'User favorites retrieved successfully',
    type: [UserFavoriteResponseDto],
    example: {
      data: [
        {
          id: 1,
          entityType: 'team',
          entityId: 33,
          entityName: 'Manchester United',
          entityLogo: 'https://media.api-sports.io/football/teams/33.png',
          isActive: true,
          notifyFixtures: true,
          notifyNews: false,
          notifyTransfers: false,
          createdAt: '2025-06-27T10:00:00Z',
          updatedAt: '2025-06-27T10:00:00Z'
        },
        {
          id: 2,
          entityType: 'league',
          entityId: 39,
          entityName: 'Premier League',
          entityLogo: 'https://media.api-sports.io/football/leagues/39.png',
          isActive: true,
          notifyFixtures: false,
          notifyNews: true,
          notifyTransfers: false,
          createdAt: '2025-06-27T09:30:00Z',
          updatedAt: '2025-06-27T09:30:00Z'
        }
      ],
      status: 200
    }
  })
  @ApiQuery({ 
    name: 'entityType', 
    enum: FavoriteEntityType, 
    required: false,
    description: 'Filter by entity type (team, league, player)',
    example: 'team'
  })
  @ApiQuery({ 
    name: 'isActive', 
    type: Boolean, 
    required: false,
    description: 'Filter by active status (true = active favorites, false = disabled favorites)',
    example: true
  })
  @ApiQuery({ 
    name: 'limit', 
    type: Number, 
    required: false,
    description: 'Maximum number of results to return (1-100, default: 50)',
    example: 20
  })
  @ApiQuery({ 
    name: 'offset', 
    type: Number, 
    required: false,
    description: 'Number of results to skip for pagination (default: 0)',
    example: 0
  })
  @Get()
  async getFavorites(
    @CurrentUser() user: RegisteredUser,
    @Query() queryDto: GetUserFavoritesDto,
  ): Promise<{ data: UserFavoriteResponseDto[]; status: number }> {
    const favorites = await this.favoriteService.getUserFavorites(user.id, queryDto);
    
    return {
      data: favorites,
      status: 200,
    };
  }

  @ApiOperation({
    summary: '📊 Get Favorites Statistics',
    description: `
    **Get comprehensive statistics about user's favorites**
    
    Returns detailed analytics about the user's favorite entities including:
    - Total count by type (teams, leagues, players)
    - Active vs inactive favorites
    - Notification preferences breakdown
    
    **Useful for:**
    - Dashboard statistics
    - User profile analytics
    - Notification management overview
    `
  })
  @ApiResponse({
    status: 200,
    description: 'User favorite statistics retrieved successfully',
    type: UserFavoriteStatsDto,
    example: {
      data: {
        totalFavorites: 15,
        totalTeams: 8,
        totalLeagues: 5,
        totalPlayers: 2,
        activeFavorites: 12,
        inactiveFavorites: 3,
        favoritesWithFixtureNotifications: 10,
        favoritesWithNewsNotifications: 3,
        favoritesWithTransferNotifications: 1
      },
      status: 200
    }
  })
  @Get('stats')
  async getFavoriteStats(
    @CurrentUser() user: RegisteredUser,
  ): Promise<{ data: UserFavoriteStatsDto; status: number }> {
    const stats = await this.favoriteService.getUserFavoriteStats(user.id);
    
    return {
      data: stats,
      status: 200,
    };
  }

  @ApiOperation({
    summary: '✏️ Update Favorite Settings',
    description: `
    **Update favorite entity settings and preferences**
    
    Use this endpoint to modify favorite settings without removing the favorite entirely.
    
    **Common Updates:**
    - Toggle active status: Temporarily disable/enable a favorite
    - Notification preferences: Control which notifications to receive
    - Entity information: Update cached name/logo if changed
    
    **Scenarios:**
    - Disable notifications during off-season: \`{"notifyFixtures": false}\`
    - Temporarily mute a favorite: \`{"isActive": false}\`
    - Enable transfer alerts: \`{"notifyTransfers": true}\`
    - Update team name after rebrand: \`{"entityName": "New Team Name"}\`
    `
  })
  @ApiResponse({
    status: 200,
    description: 'Favorite settings updated successfully',
    type: UserFavoriteResponseDto,
    example: {
      data: {
        id: 1,
        entityType: 'team',
        entityId: 33,
        entityName: 'Manchester United',
        entityLogo: 'https://media.api-sports.io/football/teams/33.png',
        isActive: false,
        notifyFixtures: false,
        notifyNews: true,
        notifyTransfers: true,
        createdAt: '2025-06-27T10:00:00Z',
        updatedAt: '2025-06-27T12:30:00Z'
      },
      status: 200,
      message: 'Favorite updated successfully'
    }
  })
  @ApiResponse({
    status: 404,
    description: 'Favorite not found or not owned by user',
    example: {
      message: 'Favorite with ID 999 not found',
      error: 'Not Found',
      statusCode: 404
    }
  })
  @ApiParam({ 
    name: 'favoriteId', 
    description: 'Unique ID of the favorite record to update (from user\'s favorites list)',
    example: 1,
    type: 'number'
  })
  @Patch(':favoriteId')
  async updateFavorite(
    @CurrentUser() user: RegisteredUser,
    @Param('favoriteId') favoriteId: string,
    @Body() updateDto: UpdateUserFavoriteDto,
  ): Promise<{ data: UserFavoriteResponseDto; status: number; message: string }> {
    const result = await this.favoriteService.updateFavorite(
      user.id,
      parseInt(favoriteId, 10),
      updateDto
    );
    
    return {
      data: result.favorite!,
      status: 200,
      message: result.message!,
    };
  }

  @ApiOperation({
    summary: '❌ Remove Favorite',
    description: `
    **Permanently remove a favorite from user's list**
    
    This action cannot be undone. The favorite will be completely removed from the database.
    
    **Alternative:** Consider using "Update Favorite" with \`isActive: false\` 
    to temporarily disable instead of permanent removal.
    
    **Use Cases:**
    - User no longer interested in the entity
    - Cleanup of old/irrelevant favorites
    - User explicitly wants to remove (not just disable)
    `
  })
  @ApiResponse({
    status: 200,
    description: 'Favorite removed successfully',
    example: {
      status: 200,
      message: 'Favorite removed successfully'
    }
  })
  @ApiResponse({
    status: 404,
    description: 'Favorite not found or not owned by user',
    example: {
      message: 'Favorite with ID 999 not found',
      error: 'Not Found',
      statusCode: 404
    }
  })
  @ApiParam({ 
    name: 'favoriteId', 
    description: 'Unique ID of the favorite record to remove (from user\'s favorites list)',
    example: 1,
    type: 'number'
  })
  @Delete(':favoriteId')
  async removeFavorite(
    @CurrentUser() user: RegisteredUser,
    @Param('favoriteId') favoriteId: string,
  ): Promise<{ status: number; message: string }> {
    const result = await this.favoriteService.removeFavorite(
      user.id,
      parseInt(favoriteId, 10)
    );
    
    return {
      status: 200,
      message: result.message!,
    };
  }

  @ApiOperation({
    summary: '✅ Check Favorite Status',
    description: `
    **Check if a specific entity is in user's favorites**
    
    Quick way to determine if a team, league, or player is already favorited by the user.
    Very useful for UI components to show correct favorite/unfavorite buttons.
    
    **Path Parameters:**
    - \`entityType\`: Type of entity (\`team\`, \`league\`, \`player\`)
    - \`entityId\`: External ID from API-Sports.io
    
    **Examples:**
    - Check Manchester United: \`GET /user/favorites/check/team/33\`
    - Check Premier League: \`GET /user/favorites/check/league/39\`
    - Check Messi: \`GET /user/favorites/check/player/276\`
    
    **Frontend Usage:**
    Use this to show proper heart/star icons in team/league/player pages.
    `
  })
  @ApiResponse({
    status: 200,
    description: 'Favorite status checked successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'object',
          properties: {
            isFavorited: { 
              type: 'boolean', 
              description: 'Whether the entity is in user favorites',
              example: true 
            },
            entityType: { 
              type: 'string', 
              enum: Object.values(FavoriteEntityType),
              description: 'Type of entity checked',
              example: 'team'
            },
            entityId: { 
              type: 'number',
              description: 'External ID of the entity',
              example: 33
            }
          }
        },
        status: { type: 'number', example: 200 }
      },
      example: {
        data: {
          isFavorited: true,
          entityType: 'team',
          entityId: 33
        },
        status: 200
      }
    }
  })
  @ApiParam({ 
    name: 'entityType', 
    enum: FavoriteEntityType,
    description: 'Type of entity to check (team, league, player)',
    example: 'team'
  })
  @ApiParam({ 
    name: 'entityId', 
    description: 'External ID of the entity from API-Sports.io (e.g., Man United = 33, Premier League = 39)',
    example: 33,
    type: 'number'
  })
  @Get('check/:entityType/:entityId')
  async checkFavorite(
    @CurrentUser() user: RegisteredUser,
    @Param('entityType') entityType: FavoriteEntityType,
    @Param('entityId') entityId: string,
  ): Promise<{ data: { isFavorited: boolean; entityType: FavoriteEntityType; entityId: number }; status: number }> {
    const isFavorited = await this.favoriteService.isFavorited(
      user.id,
      entityType,
      parseInt(entityId, 10)
    );
    
    return {
      data: {
        isFavorited,
        entityType,
        entityId: parseInt(entityId, 10),
      },
      status: 200,
    };
  }
}
