import { Entity, Column, PrimaryGeneratedColumn, Index, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Unique } from 'typeorm';
import { RegisteredUser } from './registered-user.entity';

export enum FavoriteEntityType {
  TEAM = 'team',
  LEAGUE = 'league', 
  PLAYER = 'player'
}

/**
 * User Favorite Entity
 * Stores user's favorite teams, leagues, and players
 */
@Entity('user_favorites')
@Unique(['userId', 'entityType', 'entityId'])
@Index(['userId', 'entityType'])
@Index(['entityType', 'entityId'])
export class UserFavorite {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  @Index('idx_user_favorite_user_id')
  userId: number;

  @Column({
    type: 'enum',
    enum: FavoriteEntityType
  })
  @Index('idx_user_favorite_entity_type')
  entityType: FavoriteEntityType;

  @Column()
  @Index('idx_user_favorite_entity_id')
  entityId: number; // team.externalId, league.externalId, player.externalId

  @Column({ nullable: true, length: 255 })
  entityName: string; // Cached name for quick display

  @Column({ nullable: true, length: 500 })
  entityLogo: string; // Cached logo URL

  @Column({ default: true })
  isActive: boolean; // User can temporarily disable notifications

  @Column({ default: true })
  notifyFixtures: boolean; // Notify about upcoming fixtures

  @Column({ default: false })
  notifyNews: boolean; // Notify about related news

  @Column({ default: false })
  notifyTransfers: boolean; // Notify about transfers (teams/players)

  @CreateDateColumn({ type: 'timestamp with time zone' })
  @Index('idx_user_favorite_created_at')
  createdAt: Date;

  @UpdateDateColumn({ type: 'timestamp with time zone' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => RegisteredUser, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'userId' })
  user: RegisteredUser;

  /**
   * Get display name for this favorite
   */
  getDisplayName(): string {
    return this.entityName || `${this.entityType} #${this.entityId}`;
  }

  /**
   * Check if notifications are enabled for this favorite
   */
  hasNotificationsEnabled(): boolean {
    return this.isActive && (this.notifyFixtures || this.notifyNews || this.notifyTransfers);
  }
}
