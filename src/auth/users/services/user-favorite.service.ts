import { Injectable, Logger, BadRequestException, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { UserFavorite, FavoriteEntityType } from '../entities/user-favorite.entity';
import { RegisteredUser } from '../entities/registered-user.entity';
import { CreateUserFavoriteDto, UpdateUserFavoriteDto, GetUserFavoritesDto, UserFavoriteResponseDto, UserFavoriteStatsDto } from '../dto/user-favorite.dto';
import { CacheService } from '../../../core/cache/cache.service';

export interface UserFavoriteResult {
  success: boolean;
  favorite?: UserFavorite;
  message?: string;
}

@Injectable()
export class UserFavoriteService {
  private readonly logger = new Logger(UserFavoriteService.name);

  constructor(
    @InjectRepository(UserFavorite)
    private readonly favoriteRepository: Repository<UserFavorite>,
    @InjectRepository(RegisteredUser)
    private readonly userRepository: Repository<RegisteredUser>,
    private readonly cacheService: CacheService,
  ) {}

  /**
   * Add a new favorite for user
   */
  async addFavorite(userId: number, createDto: CreateUserFavoriteDto): Promise<UserFavoriteResult> {
    try {
      // Check if user exists
      const user = await this.userRepository.findOne({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Check if favorite already exists
      const existingFavorite = await this.favoriteRepository.findOne({
        where: {
          userId,
          entityType: createDto.entityType,
          entityId: createDto.entityId,
        },
      });

      if (existingFavorite) {
        throw new ConflictException(
          `${createDto.entityType} with ID ${createDto.entityId} is already in favorites`
        );
      }

      // Create new favorite
      const favorite = this.favoriteRepository.create({
        userId,
        ...createDto,
      });

      const savedFavorite = await this.favoriteRepository.save(favorite);

      // Clear user favorites cache
      await this.clearUserCache(userId);

      this.logger.log(`User ${userId} added ${createDto.entityType} ${createDto.entityId} to favorites`);

      return {
        success: true,
        favorite: savedFavorite,
        message: 'Favorite added successfully',
      };
    } catch (error) {
      if (error instanceof BadRequestException || 
          error instanceof NotFoundException || 
          error instanceof ConflictException) {
        throw error;
      }
      
      this.logger.error(`Failed to add favorite for user ${userId}: ${error.message}`);
      throw new BadRequestException('Failed to add favorite');
    }
  }

  /**
   * Remove a favorite for user
   */
  async removeFavorite(userId: number, favoriteId: number): Promise<UserFavoriteResult> {
    try {
      const favorite = await this.favoriteRepository.findOne({
        where: { id: favoriteId, userId },
      });

      if (!favorite) {
        throw new NotFoundException('Favorite not found');
      }

      await this.favoriteRepository.remove(favorite);

      // Clear user favorites cache
      await this.clearUserCache(userId);

      this.logger.log(`User ${userId} removed favorite ${favoriteId}`);

      return {
        success: true,
        message: 'Favorite removed successfully',
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      this.logger.error(`Failed to remove favorite ${favoriteId} for user ${userId}: ${error.message}`);
      throw new BadRequestException('Failed to remove favorite');
    }
  }

  /**
   * Update a favorite
   */
  async updateFavorite(userId: number, favoriteId: number, updateDto: UpdateUserFavoriteDto): Promise<UserFavoriteResult> {
    try {
      const favorite = await this.favoriteRepository.findOne({
        where: { id: favoriteId, userId },
      });

      if (!favorite) {
        throw new NotFoundException('Favorite not found');
      }

      // Update favorite
      Object.assign(favorite, updateDto);
      const updatedFavorite = await this.favoriteRepository.save(favorite);

      // Clear user favorites cache
      await this.clearUserCache(userId);

      this.logger.log(`User ${userId} updated favorite ${favoriteId}`);

      return {
        success: true,
        favorite: updatedFavorite,
        message: 'Favorite updated successfully',
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      this.logger.error(`Failed to update favorite ${favoriteId} for user ${userId}: ${error.message}`);
      throw new BadRequestException('Failed to update favorite');
    }
  }

  /**
   * Get user's favorites
   */
  async getUserFavorites(userId: number, queryDto: GetUserFavoritesDto): Promise<UserFavoriteResponseDto[]> {
    const cacheKey = `user_favorites:${userId}:${JSON.stringify(queryDto)}`;

    try {
      // Try to get from cache
      const cached = await this.cacheService.getCache(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Build query conditions
      const where: FindOptionsWhere<UserFavorite> = { userId };
      
      if (queryDto.entityType) {
        where.entityType = queryDto.entityType;
      }
      
      if (queryDto.isActive !== undefined) {
        where.isActive = queryDto.isActive;
      }

      // Execute query
      const favorites = await this.favoriteRepository.find({
        where,
        order: { createdAt: 'DESC' },
        take: queryDto.limit || 50,
        skip: queryDto.offset || 0,
      });

      const result = favorites.map(this.mapToResponseDto);

      // Cache for 5 minutes
      await this.cacheService.setCache(cacheKey, JSON.stringify(result), 300);

      return result;
    } catch (error) {
      this.logger.error(`Failed to get favorites for user ${userId}: ${error.message}`);
      throw new BadRequestException('Failed to get favorites');
    }
  }

  /**
   * Get user's favorite statistics
   */
  async getUserFavoriteStats(userId: number): Promise<UserFavoriteStatsDto> {
    const cacheKey = `user_favorite_stats:${userId}`;

    try {
      // Try to get from cache
      const cached = await this.cacheService.getCache(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Get stats using aggregation
      const [total, teams, leagues, players, active, withNotifications] = await Promise.all([
        this.favoriteRepository.count({ where: { userId } }),
        this.favoriteRepository.count({ where: { userId, entityType: FavoriteEntityType.TEAM } }),
        this.favoriteRepository.count({ where: { userId, entityType: FavoriteEntityType.LEAGUE } }),
        this.favoriteRepository.count({ where: { userId, entityType: FavoriteEntityType.PLAYER } }),
        this.favoriteRepository.count({ where: { userId, isActive: true } }),
        this.favoriteRepository
          .createQueryBuilder('favorite')
          .where('favorite.userId = :userId', { userId })
          .andWhere('favorite.isActive = true')
          .andWhere('(favorite.notifyFixtures = true OR favorite.notifyNews = true OR favorite.notifyTransfers = true)')
          .getCount(),
      ]);

      const stats: UserFavoriteStatsDto = {
        total,
        teams,
        leagues,
        players,
        active,
        withNotifications,
      };

      // Cache for 10 minutes
      await this.cacheService.setCache(cacheKey, JSON.stringify(stats), 600);

      return stats;
    } catch (error) {
      this.logger.error(`Failed to get favorite stats for user ${userId}: ${error.message}`);
      throw new BadRequestException('Failed to get favorite statistics');
    }
  }

  /**
   * Check if user has favorited an entity
   */
  async isFavorited(userId: number, entityType: FavoriteEntityType, entityId: number): Promise<boolean> {
    try {
      const favorite = await this.favoriteRepository.findOne({
        where: { userId, entityType, entityId, isActive: true },
      });
      
      return !!favorite;
    } catch (error) {
      this.logger.error(`Failed to check favorite status: ${error.message}`);
      return false;
    }
  }

  /**
   * Get users who favorited a specific entity (for notifications)
   */
  async getUsersWhoFavorited(entityType: FavoriteEntityType, entityId: number): Promise<UserFavorite[]> {
    try {
      return await this.favoriteRepository.find({
        where: {
          entityType,
          entityId,
          isActive: true,
        },
        relations: ['user'],
      });
    } catch (error) {
      this.logger.error(`Failed to get users who favorited ${entityType} ${entityId}: ${error.message}`);
      return [];
    }
  }

  /**
   * Clear user cache
   */
  private async clearUserCache(userId: number): Promise<void> {
    try {
      await this.cacheService.deleteByPattern(`user_favorites:${userId}:*`);
      await this.cacheService.deleteByPattern(`user_favorite_stats:${userId}`);
    } catch (error) {
      this.logger.warn(`Failed to clear cache for user ${userId}: ${error.message}`);
    }
  }

  /**
   * Map entity to response DTO
   */
  private mapToResponseDto(favorite: UserFavorite): UserFavoriteResponseDto {
    return {
      id: favorite.id,
      userId: favorite.userId,
      entityType: favorite.entityType,
      entityId: favorite.entityId,
      entityName: favorite.entityName,
      entityLogo: favorite.entityLogo,
      isActive: favorite.isActive,
      notifyFixtures: favorite.notifyFixtures,
      notifyNews: favorite.notifyNews,
      notifyTransfers: favorite.notifyTransfers,
      createdAt: favorite.createdAt,
      updatedAt: favorite.updatedAt,
    };
  }
}
