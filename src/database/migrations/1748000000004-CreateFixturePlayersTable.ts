import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateFixtureLineupsTable1748000000004 implements MigrationInterface {
    name = 'CreateFixtureLineupsTable1748000000004';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create fixture_lineups table
        await queryRunner.query(`
            CREATE TABLE "fixture_lineups" (
                "id" SERIAL NOT NULL,
                "fixtureId" integer NOT NULL,
                "teamId" integer NOT NULL,
                "teamName" character varying NOT NULL,
                "teamLogo" character varying,
                "formation" character varying,
                "playerId" integer NOT NULL,
                "playerName" character varying NOT NULL,
                "playerNumber" integer,
                "playerPosition" character varying,
                "playerGrid" character varying,
                "isStarting" boolean NOT NULL DEFAULT false,
                "teamColors" jsonb,
                "coach" jsonb,
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                CONSTRAINT "PK_fixture_lineups_id" PRIMARY KEY ("id")
            )
        `);

        // Create indexes for faster queries
        await queryRunner.query(`
            CREATE INDEX "IDX_fixture_lineups_fixtureId" ON "fixture_lineups" ("fixtureId")
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_fixture_lineups_teamId" ON "fixture_lineups" ("teamId")
        `);

        await queryRunner.query(`
            CREATE INDEX "IDX_fixture_lineups_playerId" ON "fixture_lineups" ("playerId")
        `);

        // Create foreign key constraint to players table
        await queryRunner.query(`
            ALTER TABLE "fixture_lineups"
            ADD CONSTRAINT "FK_fixture_lineups_playerId"
            FOREIGN KEY ("playerId") REFERENCES "players"("id") ON DELETE CASCADE
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop foreign key constraint first
        await queryRunner.query(`ALTER TABLE "fixture_lineups" DROP CONSTRAINT "FK_fixture_lineups_playerId"`);

        // Drop indexes
        await queryRunner.query(`DROP INDEX "IDX_fixture_lineups_playerId"`);
        await queryRunner.query(`DROP INDEX "IDX_fixture_lineups_teamId"`);
        await queryRunner.query(`DROP INDEX "IDX_fixture_lineups_fixtureId"`);

        // Drop table
        await queryRunner.query(`DROP TABLE "fixture_lineups"`);
    }
}
