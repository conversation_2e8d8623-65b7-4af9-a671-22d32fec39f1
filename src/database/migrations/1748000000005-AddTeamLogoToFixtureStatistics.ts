import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTeamLogoToFixtureStatistics1748000000005 implements MigrationInterface {
    name = 'AddTeamLogoToFixtureStatistics1748000000005';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add teamLogo column to fixture_statistics table
        await queryRunner.query(`
            ALTER TABLE "fixture_statistics" 
            ADD COLUMN "teamLogo" character varying
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove teamLogo column from fixture_statistics table
        await queryRunner.query(`
            ALTER TABLE "fixture_statistics" 
            DROP COLUMN "teamLogo"
        `);
    }
}
