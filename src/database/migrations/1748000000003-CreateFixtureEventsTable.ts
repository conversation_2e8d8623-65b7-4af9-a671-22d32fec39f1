import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateFixtureEventsTable1748000000003 implements MigrationInterface {
    name = 'CreateFixtureEventsTable1748000000003';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Create fixture_events table
        await queryRunner.query(`
            CREATE TABLE "fixture_events" (
                "id" SERIAL NOT NULL,
                "fixtureId" integer NOT NULL,
                "events" jsonb NOT NULL,
                "createdAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updatedAt" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                CONSTRAINT "PK_fixture_events_id" PRIMARY KEY ("id")
            )
        `);

        // Create index on fixtureId for faster queries
        await queryRunner.query(`
            CREATE INDEX "IDX_fixture_events_fixtureId" ON "fixture_events" ("fixtureId")
        `);

        // Create unique constraint on fixtureId (one events record per fixture)
        await queryRunner.query(`
            CREATE UNIQUE INDEX "UQ_fixture_events_fixtureId" ON "fixture_events" ("fixtureId")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop indexes first
        await queryRunner.query(`DROP INDEX "UQ_fixture_events_fixtureId"`);
        await queryRunner.query(`DROP INDEX "IDX_fixture_events_fixtureId"`);
        
        // Drop table
        await queryRunner.query(`DROP TABLE "fixture_events"`);
    }
}
