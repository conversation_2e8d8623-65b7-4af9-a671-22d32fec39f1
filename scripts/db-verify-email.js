#!/usr/bin/env node

/**
 * 💾 Database Email Verification Helper
 * Sets isEmailVerified = true for a user by email
 * Usage: node scripts/db-verify-email.js <email>
 */

const { Pool } = require('pg');
require('dotenv').config();

// Database configuration from .env
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'testlivesport',
  user: process.env.DB_USER || 'postgresuser',
  password: process.env.DB_PASSWORD || '831993da',
});

async function verifyUserEmail(email) {
  const client = await pool.connect();
  try {
    console.log('🔍 Looking for user with email:', email);
    
    // First, check if user exists
    const userResult = await client.query(
      'SELECT id, email, "isEmailVerified" FROM registered_users WHERE email = $1',
      [email]
    );
    
    if (userResult.rows.length === 0) {
      console.error('❌ User not found with email:', email);
      return false;
    }
    
    const user = userResult.rows[0];
    console.log('👤 Found user:', {
      id: user.id,
      email: user.email,
      isEmailVerified: user.isEmailVerified
    });
    
    if (user.isEmailVerified) {
      console.log('✅ User email is already verified!');
      return true;
    }
    
    // Update isEmailVerified to true
    console.log('🔄 Setting isEmailVerified = true...');
    const updateResult = await client.query(
      'UPDATE registered_users SET "isEmailVerified" = true WHERE email = $1 RETURNING id, email, "isEmailVerified"',
      [email]
    );
    
    if (updateResult.rows.length > 0) {
      const updatedUser = updateResult.rows[0];
      console.log('✅ Email verification updated successfully!');
      console.log('👤 Updated user:', {
        id: updatedUser.id,
        email: updatedUser.email,
        isEmailVerified: updatedUser.isEmailVerified
      });
      return true;
    } else {
      console.error('❌ Failed to update email verification');
      return false;
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
    return false;
  } finally {
    client.release();
  }
}

async function main() {
  const email = process.argv[2];
  
  if (!email) {
    console.error('❌ Usage: node scripts/db-verify-email.js <email>');
    console.error('   Example: node scripts/db-verify-email.js <EMAIL>');
    process.exit(1);
  }
  
  console.log('🚀 Connecting to database...');
  console.log('📊 Database:', process.env.DB_NAME);
  console.log('🏠 Host:', process.env.DB_HOST);
  console.log('🔌 Port:', process.env.DB_PORT);
  
  try {
    const success = await verifyUserEmail(email);
    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('💥 Unexpected error:', error.message);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n🛑 Shutting down...');
  await pool.end();
  process.exit(0);
});

main();
