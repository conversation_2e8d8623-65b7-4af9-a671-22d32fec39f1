#!/bin/bash

# Monitor Dual Port Setup
# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔄 Dual Port Monitoring - ecosystem.config.js${NC}"
echo "=================================================="

echo -e "${YELLOW}📊 PM2 Status:${NC}"
pm2 list

echo ""
echo -e "${YELLOW}🌐 Port Status:${NC}"
ss -tlnp | grep :300

echo ""
echo -e "${YELLOW}🧪 API Testing:${NC}"

# Test Port 3000
echo -n "Port 3000: "
RESPONSE_3000=$(curl -s -w "%{http_code}" http://localhost:3000/football/leagues?limit=1)
STATUS_3000=$(echo "$RESPONSE_3000" | tail -c 3)
if [ "$STATUS_3000" = "200" ]; then
    echo -e "${GREEN}✅ Working (Status: $STATUS_3000)${NC}"
else
    echo -e "${RED}❌ Failed (Status: $STATUS_3000)${NC}"
fi

# Test Port 3001
echo -n "Port 3001: "
RESPONSE_3001=$(curl -s -w "%{http_code}" http://localhost:3001/football/leagues?limit=1)
STATUS_3001=$(echo "$RESPONSE_3001" | tail -c 3)
if [ "$STATUS_3001" = "200" ]; then
    echo -e "${GREEN}✅ Working (Status: $STATUS_3001)${NC}"
else
    echo -e "${RED}❌ Failed (Status: $STATUS_3001)${NC}"
fi

echo ""
echo -e "${YELLOW}⚡ Load Testing (10 requests each):${NC}"

# Load test Port 3000
echo -n "Port 3000 load test: "
for i in {1..10}; do
    curl -s http://localhost:3000/football/leagues?limit=1 > /dev/null
    echo -n "."
done
echo -e " ${GREEN}Done${NC}"

# Load test Port 3001
echo -n "Port 3001 load test: "
for i in {1..10}; do
    curl -s http://localhost:3001/football/leagues?limit=1 > /dev/null
    echo -n "."
done
echo -e " ${GREEN}Done${NC}"

echo ""
echo -e "${YELLOW}📈 Instance Performance:${NC}"
pm2 show 0 | grep -E "(cpu|memory|restarts)"
echo ""
pm2 show 2 | grep -E "(cpu|memory|restarts)"

echo ""
echo -e "${BLUE}🎯 Summary:${NC}"
echo "✅ Cluster Mode: 2 instances on separate ports"
echo "✅ Port 3000: Instance 0 (api-sports-game)"
echo "✅ Port 3001: Instance 2 (api-sports-game)"
echo "✅ Worker: Instance 1 (auto-update-sports-game)"
echo "✅ Load Balancing: Manual (client chooses port)"
echo ""
echo -e "${GREEN}🚀 ecosystem.config.js working perfectly with dual ports!${NC}"
