#!/bin/bash

echo "🚀 Testing User Favorites System (Phase 2)"
echo "=========================================="

# API Base URL
BASE_URL="http://localhost:3000"

# Test user token (replace with real token)
USER_TOKEN="your_user_jwt_token_here"

echo ""
echo "Note: You need a valid user JWT token to test this API"
echo "To get a token, register/login via /auth/users/register or /auth/users/login"
echo ""

if [ "$USER_TOKEN" = "your_user_jwt_token_here" ]; then
    echo "⚠️  Please update USER_TOKEN in this script with a real JWT token"
    echo "Example commands to get token:"
    echo ""
    echo "# Register new user"
    echo "curl -X POST '$BASE_URL/auth/users/register' \\"
    echo "  -H 'Content-Type: application/json' \\"
    echo "  -d '{"
    echo "    \"username\": \"testuser\","
    echo "    \"email\": \"<EMAIL>\","
    echo "    \"password\": \"password123\","
    echo "    \"fullName\": \"Test User\""
    echo "  }'"
    echo ""
    echo "# Login"
    echo "curl -X POST '$BASE_URL/auth/users/login' \\"
    echo "  -H 'Content-Type: application/json' \\"
    echo "  -d '{"
    echo "    \"username\": \"testuser\","
    echo "    \"password\": \"password123\""
    echo "  }'"
    echo ""
    exit 1
fi

echo ""
echo "1. Getting user favorite statistics..."
curl -s "$BASE_URL/user/favorites/stats" \
  -H "Authorization: Bearer $USER_TOKEN" | python3 -m json.tool

echo ""
echo ""
echo "2. Adding a team to favorites..."
curl -s -X POST "$BASE_URL/user/favorites" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "entityType": "team",
    "entityId": 33,
    "entityName": "Manchester United",
    "entityLogo": "https://media.api-sports.io/football/teams/33.png",
    "notifyFixtures": true,
    "notifyNews": false
  }' | python3 -m json.tool

echo ""
echo ""
echo "3. Adding a league to favorites..."
curl -s -X POST "$BASE_URL/user/favorites" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "entityType": "league",
    "entityId": 39,
    "entityName": "Premier League",
    "entityLogo": "https://media.api-sports.io/football/leagues/39.png",
    "notifyFixtures": true,
    "notifyNews": true
  }' | python3 -m json.tool

echo ""
echo ""
echo "4. Getting all user favorites..."
curl -s "$BASE_URL/user/favorites" \
  -H "Authorization: Bearer $USER_TOKEN" | python3 -m json.tool

echo ""
echo ""
echo "5. Getting only team favorites..."
curl -s "$BASE_URL/user/favorites?entityType=team" \
  -H "Authorization: Bearer $USER_TOKEN" | python3 -m json.tool

echo ""
echo ""
echo "6. Checking if Manchester United is favorited..."
curl -s "$BASE_URL/user/favorites/check/team/33" \
  -H "Authorization: Bearer $USER_TOKEN" | python3 -m json.tool

echo ""
echo ""
echo "7. Getting updated favorite statistics..."
curl -s "$BASE_URL/user/favorites/stats" \
  -H "Authorization: Bearer $USER_TOKEN" | python3 -m json.tool

echo ""
echo ""
echo "8. Updating a favorite (disable notifications)..."
# Note: Replace '1' with actual favorite ID from step 4
curl -s -X PATCH "$BASE_URL/user/favorites/1" \
  -H "Authorization: Bearer $USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "notifyFixtures": false,
    "isActive": true
  }' | python3 -m json.tool

echo ""
echo ""
echo "9. Removing a favorite..."
# Note: Replace '2' with actual favorite ID from step 4
curl -s -X DELETE "$BASE_URL/user/favorites/2" \
  -H "Authorization: Bearer $USER_TOKEN" | python3 -m json.tool

echo ""
echo "✅ User Favorites test completed!"
echo ""
echo "📋 Summary of Phase 2 Features:"
echo "- ✅ Add team/league/player to favorites"
echo "- ✅ Get user favorites with filtering"
echo "- ✅ Get favorite statistics"
echo "- ✅ Check if entity is favorited"
echo "- ✅ Update favorite settings"
echo "- ✅ Remove favorites"
echo "- ✅ Notification preferences"
echo "- ✅ JWT authentication required"
