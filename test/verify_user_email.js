#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { Client } = require('pg');

// Đọc file .env
function loadEnvFile() {
    const envPath = path.join(__dirname, '.env');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const env = {};
    
    envContent.split('\n').forEach(line => {
        const trimmed = line.trim();
        if (trimmed && !trimmed.startsWith('#')) {
            const [key, ...valueParts] = trimmed.split('=');
            if (key && valueParts.length > 0) {
                env[key] = valueParts.join('=');
            }
        }
    });
    
    return env;
}

// Kết nối database và verify email
async function verifyUserEmail(email) {
    const env = loadEnvFile();
    
    const client = new Client({
        host: env.DB_HOST,
        port: parseInt(env.DB_PORT),
        user: env.DB_USER,
        password: env.DB_PASSWORD,
        database: env.DB_NAME,
    });

    try {
        await client.connect();
        console.log('✅ Connected to database successfully');
        
        // Tìm user theo email
        const userQuery = 'SELECT id, email, "isEmailVerified" FROM registered_users WHERE email = $1';
        const userResult = await client.query(userQuery, [email]);
        
        if (userResult.rows.length === 0) {
            console.log(`❌ User with email ${email} not found`);
            return false;
        }
        
        const user = userResult.rows[0];
        console.log(`📧 Found user: ID=${user.id}, Email=${user.email}, Verified=${user.isEmailVerified}`);
        
        if (user.isEmailVerified) {
            console.log('✅ User is already verified');
            return true;
        }
        
        // Update user để verify email
        const updateQuery = 'UPDATE registered_users SET "isEmailVerified" = true WHERE email = $1';
        await client.query(updateQuery, [email]);
        
        console.log('✅ Email verified successfully in database');
        return true;
        
    } catch (error) {
        console.error('❌ Database error:', error.message);
        return false;
    } finally {
        await client.end();
    }
}

// Main function
async function main() {
    const email = process.argv[2];
    
    if (!email) {
        console.log('Usage: node verify_user_email.js <email>');
        console.log('Example: node verify_user_email.js <EMAIL>');
        process.exit(1);
    }
    
    console.log(`🔍 Verifying email for: ${email}`);
    const success = await verifyUserEmail(email);
    
    process.exit(success ? 0 : 1);
}

if (require.main === module) {
    main();
}
