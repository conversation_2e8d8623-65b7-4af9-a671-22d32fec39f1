const { DataSource } = require('typeorm');
const { CreateFixtureViewsTable1719504000000 } = require('./dist/src/migrations/1719504000000-CreateFixtureViewsTable.js');

require('dotenv').config({ path: '.env.api' });

async function testMigration() {
    console.log('🔄 Testing Migration...');
    
    // Create data source
    const dataSource = new DataSource({
        type: 'postgres',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT) || 5432,
        username: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
        entities: [],
        migrations: [],
        synchronize: false,
        logging: true,
    });

    try {
        await dataSource.initialize();
        console.log('✅ Database connected');

        // Check if fixture_views table exists
        const tableExists = await dataSource.query(`
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'fixture_views'
            );
        `);
        
        console.log(`Table fixture_views exists: ${tableExists[0].exists}`);
        
        // Check if viewCount and hotSince columns exist in fixtures table
        const columnsExist = await dataSource.query(`
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'fixtures' 
            AND column_name IN ('viewCount', 'hotSince');
        `);
        
        console.log(`Fixture columns: ${JSON.stringify(columnsExist)}`);
        
        if (!tableExists[0].exists) {
            console.log('🔧 Running migration...');
            const migration = new CreateFixtureViewsTable1719504000000();
            await migration.up(dataSource.createQueryRunner());
            console.log('✅ Migration completed');
        }

    } catch (error) {
        console.error('❌ Migration failed:', error.message);
    } finally {
        await dataSource.destroy();
        console.log('🔌 Database disconnected');
    }
}

testMigration().catch(console.error);
