const nodemailer = require('nodemailer');

// Test SMTP configuration
async function testEmailConfig() {
    const transporter = nodemailer.createTransporter({
        host: 'smtp.gmail.com',
        port: 587,
        secure: false,
        auth: {
            user: '<EMAIL>',        // Thay bằng email của bạn
            pass: 'your-16-digit-app-password'   // Thay bằng app password
        }
    });

    try {
        // Verify connection
        await transporter.verify();
        console.log('✅ SMTP connection verified successfully');
        
        // Send test email
        const info = await transporter.sendMail({
            from: '<EMAIL>',
            to: '<EMAIL>',    // Thay bằng email nhận test
            subject: 'Test Email from APISportsGame',
            html: '<h1>Email system working!</h1><p>Your email configuration is working correctly.</p>'
        });
        
        console.log('✅ Test email sent:', info.messageId);
        console.log('Preview URL:', nodemailer.getTestMessageUrl(info));
        
    } catch (error) {
        console.error('❌ Email test failed:', error);
    }
}

testEmailConfig();
