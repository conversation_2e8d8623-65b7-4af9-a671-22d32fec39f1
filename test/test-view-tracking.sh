#!/bin/bash

echo "🚀 Testing View Tracking System Implementation"
echo "============================================="

# API Base URL
BASE_URL="http://localhost:3000"

echo ""
echo "1. Testing Database Schema..."
curl -s "$BASE_URL/test/database/schema-check" | python3 -m json.tool

echo ""
echo ""
echo "2. Testing View Tracking API..."

# Test with fixture ID 1
FIXTURE_ID=1
echo "📊 Tracking view for fixture $FIXTURE_ID..."

curl -s -X POST "$BASE_URL/football/fixture-views/track/$FIXTURE_ID" \
  -H "Content-Type: application/json" \
  -d '{
    "ipAddress": "127.0.0.1",
    "userAgent": "Test Browser"
  }' | python3 -m json.tool

echo ""
echo ""
echo "3. Getting View Statistics..."
curl -s "$BASE_URL/football/fixture-views/stats/$FIXTURE_ID" | python3 -m json.tool

echo ""
echo ""
echo "4. Getting Trending Fixtures..."
curl -s "$BASE_URL/football/fixture-views/trending?limit=5" | python3 -m json.tool

echo ""
echo ""
echo "5. Testing Enhanced Fixture Details (with viewStats)..."
curl -s "$BASE_URL/football/fixtures/$FIXTURE_ID" | python3 -m json.tool

echo ""
echo "✅ Test completed!"
