#!/bin/bash

echo "🧪 Testing View Tracking & User Favorites Implementation"
echo "======================================================="

BASE_URL="http://localhost:3000"

# Test 1: Get fixtures (check if viewCount is included)
echo -e "\n1️⃣ Testing Fixtures API (checking viewCount field)..."
curl -s "$BASE_URL/football/fixtures?limit=5" | jq '.data[0] | {id, homeTeam: .homeTeam.name, awayTeam: .awayTeam.name, viewCount, isHot}'

# Test 2: Get fixture detail (check viewStats and favoriteStatus)
echo -e "\n2️⃣ Testing Fixture Detail API (checking viewStats)..."
FIXTURE_ID=$(curl -s "$BASE_URL/football/fixtures?limit=1" | jq -r '.data[0].externalId')
echo "Using fixture ID: $FIXTURE_ID"
curl -s "$BASE_URL/football/fixtures/$FIXTURE_ID" | jq '{id, homeTeam: .homeTeam.name, awayTeam: .awayTeam.name, viewStats, favoriteStatus}'

# Test 3: Track view for fixture (without auth - should work for anonymous users)
echo -e "\n3️⃣ Testing View Tracking API..."
curl -X POST "$BASE_URL/football/fixtures/$FIXTURE_ID/view" \
  -H "Content-Type: application/json" \
  -d '{}' | jq '.'

# Test 4: Get view stats
echo -e "\n4️⃣ Testing View Stats API..."
curl -s "$BASE_URL/football/fixtures/$FIXTURE_ID/view-stats" | jq '.'

# Test 5: Get trending fixtures
echo -e "\n5️⃣ Testing Trending Fixtures API..."
curl -s "$BASE_URL/football/fixtures/trending" | jq '.data[0] | {id, homeTeam: .homeTeam.name, awayTeam: .awayTeam.name, viewCount}'

# Test 6: User favorites API (without auth - should return 401)
echo -e "\n6️⃣ Testing User Favorites API (without auth - expecting 401)..."
curl -s "$BASE_URL/user/favorites" | jq '.'

echo -e "\n✅ Implementation test completed!"
