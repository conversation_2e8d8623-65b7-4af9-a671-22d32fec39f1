#!/bin/bash

# 🧪 User Favorites System - Complete Test with Node.js DB Helper
# Full workflow: Register → Verify Email (via Node.js) → Login → Test Favorites
# Documentation: http://localhost:3000/api-docs#/User%20-%20Favorites%20💗

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# API Base URL
BASE_URL="http://localhost:3000"

# Test user data
TEST_USER_EMAIL="testuser$(date +%s)@example.com"
TEST_USERNAME="testuser$(date +%s)"
TEST_PASSWORD="testpassword123"

echo -e "${BLUE}🧪 User Favorites System - Complete Test with Node.js DB Helper${NC}"
echo -e "${BLUE}===============================================================${NC}"
echo -e "${CYAN}📝 This script will:${NC}"
echo -e "${CYAN}   1. Register a new user${NC}"
echo -e "${CYAN}   2. Use Node.js script to verify email in database${NC}"
echo -e "${CYAN}   3. Login the user${NC}"
echo -e "${CYAN}   4. Test all User Favorites endpoints with real data${NC}"

# Function to print test step
print_step() {
    echo -e "\n${YELLOW}📝 Step $1: $2${NC}"
}

# Function to print success
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to print info
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to make API request with better error handling
make_request() {
    local method="$1"
    local url="$2"
    local data="$3"
    local auth_header="$4"
    
    if [[ -n "$auth_header" ]]; then
        if [[ -n "$data" ]]; then
            curl -s -X "$method" "$url" \
                -H "Content-Type: application/json" \
                -H "$auth_header" \
                -d "$data"
        else
            curl -s -X "$method" "$url" \
                -H "Content-Type: application/json" \
                -H "$auth_header"
        fi
    else
        if [[ -n "$data" ]]; then
            curl -s -X "$method" "$url" \
                -H "Content-Type: application/json" \
                -d "$data"
        else
            curl -s -X "$method" "$url" \
                -H "Content-Type: application/json"
        fi
    fi
}

# Check if API server is running
print_step "0" "Checking if API server is running"
if ! curl -s "${BASE_URL}/health" > /dev/null 2>&1; then
    print_error "API server is not running at ${BASE_URL}"
    print_info "Please start the server first: npm run start:dev"
    exit 1
fi
print_success "API server is running"

# Step 1: Register a new user
print_step "1" "Registering new user"
echo -e "${PURPLE}👤 User details:${NC}"
echo -e "   Email: ${TEST_USER_EMAIL}"
echo -e "   Username: ${TEST_USERNAME}"
echo -e "   Password: ${TEST_PASSWORD}"

REGISTER_DATA="{
    \"email\": \"${TEST_USER_EMAIL}\",
    \"username\": \"${TEST_USERNAME}\",
    \"password\": \"${TEST_PASSWORD}\",
    \"firstName\": \"Test\",
    \"lastName\": \"User\"
}"

REGISTER_RESPONSE=$(make_request "POST" "${BASE_URL}/auth/register" "$REGISTER_DATA")
echo -e "${PURPLE}📥 Register Response:${NC}"
echo "$REGISTER_RESPONSE" | jq '.' 2>/dev/null || echo "$REGISTER_RESPONSE"

# Extract user ID from registration response
USER_ID=$(echo "$REGISTER_RESPONSE" | jq -r '.user.id // .id // empty' 2>/dev/null)

if [[ -z "$USER_ID" || "$USER_ID" == "null" ]]; then
    print_error "Failed to register user or extract user ID"
    echo -e "${RED}Response: $REGISTER_RESPONSE${NC}"
    exit 1
fi

print_success "User registered successfully (ID: $USER_ID)"

# Step 2: Verify email using Node.js database helper
print_step "2" "Verifying email using Node.js database helper"
print_info "Running: node scripts/db-verify-email.js ${TEST_USER_EMAIL}"

# Check if the Node.js script exists
if [[ ! -f "scripts/db-verify-email.js" ]]; then
    print_error "Database helper script not found at scripts/db-verify-email.js"
    exit 1
fi

# Run the Node.js database helper
if node scripts/db-verify-email.js "$TEST_USER_EMAIL"; then
    print_success "Email verification completed via Node.js database helper"
else
    print_error "Failed to verify email via database helper"
    exit 1
fi

# Step 3: Login the user
print_step "3" "Logging in the user"
LOGIN_DATA="{
    \"email\": \"${TEST_USER_EMAIL}\",
    \"password\": \"${TEST_PASSWORD}\"
}"

LOGIN_RESPONSE=$(make_request "POST" "${BASE_URL}/auth/login" "$LOGIN_DATA")
echo -e "${PURPLE}📥 Login Response:${NC}"
echo "$LOGIN_RESPONSE" | jq '.' 2>/dev/null || echo "$LOGIN_RESPONSE"

# Extract access token
ACCESS_TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.access_token // .accessToken // empty' 2>/dev/null)

if [[ -z "$ACCESS_TOKEN" || "$ACCESS_TOKEN" == "null" ]]; then
    print_error "Failed to login or extract access token"
    echo -e "${RED}Response: $LOGIN_RESPONSE${NC}"
    exit 1
fi

print_success "Login successful"
AUTH_HEADER="Authorization: Bearer $ACCESS_TOKEN"

# Step 4: Test User Favorites endpoints
print_step "4" "Testing User Favorites endpoints with real data"

# 4.1: Get user's favorites (should be empty initially)
print_step "4.1" "Getting user's current favorites"
FAVORITES_RESPONSE=$(make_request "GET" "${BASE_URL}/user-favorites" "" "$AUTH_HEADER")
echo -e "${PURPLE}📥 Current Favorites:${NC}"
echo "$FAVORITES_RESPONSE" | jq '.' 2>/dev/null || echo "$FAVORITES_RESPONSE"

# 4.2: Add a team to favorites (Real team: Manchester United - ID 33)
print_step "4.2" "Adding Manchester United team to favorites"
ADD_TEAM_DATA="{
    \"entityId\": 33,
    \"entityType\": \"team\"
}"

ADD_TEAM_RESPONSE=$(make_request "POST" "${BASE_URL}/user-favorites" "$ADD_TEAM_DATA" "$AUTH_HEADER")
echo -e "${PURPLE}📥 Add Team Response:${NC}"
echo "$ADD_TEAM_RESPONSE" | jq '.' 2>/dev/null || echo "$ADD_TEAM_RESPONSE"

if echo "$ADD_TEAM_RESPONSE" | jq -e '.id' > /dev/null 2>&1; then
    print_success "Team added to favorites successfully"
    TEAM_FAVORITE_ID=$(echo "$ADD_TEAM_RESPONSE" | jq -r '.id')
else
    print_error "Failed to add team to favorites"
fi

# 4.3: Add a league to favorites (Real league: Premier League - ID 39)
print_step "4.3" "Adding Premier League to favorites"
ADD_LEAGUE_DATA="{
    \"entityId\": 39,
    \"entityType\": \"league\"
}"

ADD_LEAGUE_RESPONSE=$(make_request "POST" "${BASE_URL}/user-favorites" "$ADD_LEAGUE_DATA" "$AUTH_HEADER")
echo -e "${PURPLE}📥 Add League Response:${NC}"
echo "$ADD_LEAGUE_RESPONSE" | jq '.' 2>/dev/null || echo "$ADD_LEAGUE_RESPONSE"

if echo "$ADD_LEAGUE_RESPONSE" | jq -e '.id' > /dev/null 2>&1; then
    print_success "League added to favorites successfully"
    LEAGUE_FAVORITE_ID=$(echo "$ADD_LEAGUE_RESPONSE" | jq -r '.id')
else
    print_error "Failed to add league to favorites"
fi

# 4.4: Get updated favorites list
print_step "4.4" "Getting updated favorites list"
UPDATED_FAVORITES_RESPONSE=$(make_request "GET" "${BASE_URL}/user-favorites" "" "$AUTH_HEADER")
echo -e "${PURPLE}📥 Updated Favorites:${NC}"
echo "$UPDATED_FAVORITES_RESPONSE" | jq '.' 2>/dev/null || echo "$UPDATED_FAVORITES_RESPONSE"

# 4.5: Check if specific entities are favorited
print_step "4.5" "Checking if Manchester United (team 33) is favorited"
CHECK_TEAM_RESPONSE=$(make_request "GET" "${BASE_URL}/user-favorites/check/33/team" "" "$AUTH_HEADER")
echo -e "${PURPLE}📥 Team Check Response:${NC}"
echo "$CHECK_TEAM_RESPONSE" | jq '.' 2>/dev/null || echo "$CHECK_TEAM_RESPONSE"

print_step "4.6" "Checking if Premier League (league 39) is favorited"
CHECK_LEAGUE_RESPONSE=$(make_request "GET" "${BASE_URL}/user-favorites/check/39/league" "" "$AUTH_HEADER")
echo -e "${PURPLE}📥 League Check Response:${NC}"
echo "$CHECK_LEAGUE_RESPONSE" | jq '.' 2>/dev/null || echo "$CHECK_LEAGUE_RESPONSE"

# 4.7: Get favorites statistics
print_step "4.7" "Getting user favorites statistics"
STATS_RESPONSE=$(make_request "GET" "${BASE_URL}/user-favorites/stats" "" "$AUTH_HEADER")
echo -e "${PURPLE}📥 Favorites Stats:${NC}"
echo "$STATS_RESPONSE" | jq '.' 2>/dev/null || echo "$STATS_RESPONSE"

# 4.8: Test pagination
print_step "4.8" "Testing pagination (page 1, limit 10)"
PAGINATION_RESPONSE=$(make_request "GET" "${BASE_URL}/user-favorites?page=1&limit=10" "" "$AUTH_HEADER")
echo -e "${PURPLE}📥 Pagination Response:${NC}"
echo "$PAGINATION_RESPONSE" | jq '.' 2>/dev/null || echo "$PAGINATION_RESPONSE"

# 4.9: Filter by entity type
print_step "4.9" "Filtering favorites by team type"
FILTER_TEAMS_RESPONSE=$(make_request "GET" "${BASE_URL}/user-favorites?entityType=team" "" "$AUTH_HEADER")
echo -e "${PURPLE}📥 Team Favorites:${NC}"
echo "$FILTER_TEAMS_RESPONSE" | jq '.' 2>/dev/null || echo "$FILTER_TEAMS_RESPONSE"

print_step "4.10" "Filtering favorites by league type"
FILTER_LEAGUES_RESPONSE=$(make_request "GET" "${BASE_URL}/user-favorites?entityType=league" "" "$AUTH_HEADER")
echo -e "${PURPLE}📥 League Favorites:${NC}"
echo "$FILTER_LEAGUES_RESPONSE" | jq '.' 2>/dev/null || echo "$FILTER_LEAGUES_RESPONSE"

# 4.11: Update a favorite (if we have the ID)
if [[ -n "$TEAM_FAVORITE_ID" && "$TEAM_FAVORITE_ID" != "null" ]]; then
    print_step "4.11" "Updating team favorite (adding note)"
    UPDATE_DATA="{
        \"note\": \"My favorite Premier League team!\"
    }"
    
    UPDATE_RESPONSE=$(make_request "PATCH" "${BASE_URL}/user-favorites/${TEAM_FAVORITE_ID}" "$UPDATE_DATA" "$AUTH_HEADER")
    echo -e "${PURPLE}📥 Update Response:${NC}"
    echo "$UPDATE_RESPONSE" | jq '.' 2>/dev/null || echo "$UPDATE_RESPONSE"
    
    if echo "$UPDATE_RESPONSE" | jq -e '.note' > /dev/null 2>&1; then
        print_success "Favorite updated successfully"
    else
        print_error "Failed to update favorite"
    fi
fi

# 4.12: Test fixture integration (if implemented)
print_step "4.12" "Testing fixture integration with favorite teams"
FIXTURE_INTEGRATION_RESPONSE=$(make_request "GET" "${BASE_URL}/user-favorites/fixtures" "" "$AUTH_HEADER")
echo -e "${PURPLE}📥 Fixture Integration Response:${NC}"
echo "$FIXTURE_INTEGRATION_RESPONSE" | jq '.' 2>/dev/null || echo "$FIXTURE_INTEGRATION_RESPONSE"

# 4.13: Remove one favorite for cleanup test
if [[ -n "$LEAGUE_FAVORITE_ID" && "$LEAGUE_FAVORITE_ID" != "null" ]]; then
    print_step "4.13" "Removing league favorite (cleanup test)"
    REMOVE_RESPONSE=$(make_request "DELETE" "${BASE_URL}/user-favorites/${LEAGUE_FAVORITE_ID}" "" "$AUTH_HEADER")
    echo -e "${PURPLE}📥 Remove Response:${NC}"
    echo "$REMOVE_RESPONSE" | jq '.' 2>/dev/null || echo "$REMOVE_RESPONSE"
    
    if [[ -z "$REMOVE_RESPONSE" ]] || echo "$REMOVE_RESPONSE" | jq -e '.message' > /dev/null 2>&1; then
        print_success "League favorite removed successfully"
    else
        print_error "Failed to remove league favorite"
    fi
fi

# Final summary
echo -e "\n${GREEN}🎉 User Favorites System Test Completed!${NC}"
echo -e "${GREEN}=========================================${NC}"
echo -e "${CYAN}📋 Test Summary:${NC}"
echo -e "   ✅ User Registration: ${TEST_USER_EMAIL}"
echo -e "   ✅ Email Verification: via Node.js database helper"
echo -e "   ✅ User Login: Access token obtained"
echo -e "   ✅ Add Team Favorite: Manchester United (ID: 33)"
echo -e "   ✅ Add League Favorite: Premier League (ID: 39)"
echo -e "   ✅ List Favorites: Retrieved user favorites"
echo -e "   ✅ Check Favorites: Verified specific entities"
echo -e "   ✅ Favorites Stats: Retrieved statistics"
echo -e "   ✅ Pagination: Tested page/limit parameters"
echo -e "   ✅ Filtering: Tested by entity type"
echo -e "   ✅ Update Favorite: Added note to team favorite"
echo -e "   ✅ Fixture Integration: Tested favorite team fixtures"
echo -e "   ✅ Remove Favorite: Cleanup test completed"

echo -e "\n${BLUE}🌐 Access Swagger Documentation:${NC}"
echo -e "${BLUE}   http://localhost:3000/api-docs#/User%20-%20Favorites%20💗${NC}"

echo -e "\n${YELLOW}💡 Next Steps:${NC}"
echo -e "   • Frontend can use these endpoints with the access token"
echo -e "   • All real data from API-Sports.io (Manchester United, Premier League)"
echo -e "   • Database email verification automated via Node.js helper"
echo -e "   • Full CRUD operations tested and documented"
